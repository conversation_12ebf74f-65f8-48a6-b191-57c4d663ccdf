# ms-sms-transmitter

## Overview
The `ms-sms-transmitter` is a microservice designed to handle SMS transmission requests. It processes these requests, determines the appropriate integration channel (e.g., Tibco, Apache, DXL), and sends the SMS to the intended recipient. The service is built using Spring WebFlux, making it fully reactive and non-blocking.

## Features
- **Routing SMS Requests**: Routes SMS requests to the appropriate integration channel based on short codes.
- **Integration with External APIs**: Supports integration with Tibco, Apache, and DXL for SMS delivery.
- **Reactive Programming**: Utilizes `Mono` and `Flux` for asynchronous operations.
- **Error Handling**: Centralized error handling for robust operations.
- **Caching**: Uses Redis for caching authentication tokens and menu data.
- **Streaming**: Supports Kafka and MongoDB for streaming SMS metrics.
- **Message Conversion**: Converts structured message formats (e.g., buttons, menus) into plain text for SMS delivery.
- **Authentication and Authorization**: Manages authentication tokens for external API integrations.
- **Asynchronous Processing**: Immediately acknowledges requests and processes them asynchronously.
- **ChatBot Integration**: Receives SMS requests from RabbitMQ queues for chatbot interactions.

## Architecture
The service follows a modular architecture with the following key components:
- **Controllers**: Handles incoming HTTP requests with two API versions.
- **Services**: Contains business logic for SMS processing and integration.
- **Data Layer**: Manages MongoDB interactions for storing menus and authentication tokens.
- **Integration Layer**: Handles communication with external SMS gateways.
- **Utilities**: Provides helper methods for common tasks.

### Dependency Management
The service uses a clean dependency structure to avoid circular dependencies:
- **Dependency Inversion**: Interfaces are used to decouple components and allow for easier testing and maintenance.
- **Separation of Concerns**: Each component has a single responsibility, making the system more modular and maintainable.
- **Fallback Pattern**: The `FallbackService` provides a clean way to handle integration failures without creating circular dependencies.

### Architecture Diagram

```
+------------------+      +------------------+      +------------------+
|                  |      |                  |      |                  |
|  API Controllers |----->|  SMS Processor  |----->| Request Router   |
|  (v1/v2)         |      |                  |      |                  |
+------------------+      +------------------+      +--------+---------+
                                                             |
                                                             v
+------------------+      +------------------+      +--------+---------+
|                  |      |                  |      |                  |
|  Streaming       |<-----|  Integration    |<-----| Message          |
|  Service         |      |  Processors     |      | Conversion       |
+------------------+      +--------+--------+      +------------------+
        |                         |                        ^
        v                         v                        |
+------------------+      +------------------+      +------+-----------+
|                  |      |                  |      |                  |
|  MongoDB/Kafka   |      |  External SMS    |<---->| Fallback        |
|  (Metrics)       |      |  Gateways        |      | Service         |
+------------------+      +------------------+      +------------------+
```

## Key Components
- **MessageConversionService**: Converts structured messages (buttons, menus) into SMS-compatible formats.
- **SmsRequestRouter**: Routes SMS requests to the appropriate integration channel based on short codes.
- **SmsMessageProcessor**: Processes and validates SMS requests before routing.
- **Integration Processors**: Channel-specific implementations (DXL, Tibco, Apache) for SMS delivery.
- **StreamingService**: Streams SMS metrics to Kafka or MongoDB for analytics.
- **FallbackService**: Manages fallback routing to alternative integration channels when the primary channel fails.

## How It Works
1. **Request Entry**: SMS requests are received via REST APIs (v1/v2) or RabbitMQ queues.
2. **Authentication**: Requests are authenticated using tokens that are validated against the database.
3. **Message Processing**: Structured messages are converted to plain text if needed.
4. **Routing**: The `SmsRequestRouter` determines the appropriate integration channel based on the short code.
5. **Integration**: The selected processor constructs the payload and headers for the external API.
6. **Delivery**: The SMS is sent to the external gateway for delivery to the recipient.
7. **Fallback Handling**: If the primary integration channel fails, the `FallbackService` routes the request to an alternative channel based on configuration.
8. **Streaming**: Metrics are streamed to Kafka or MongoDB for analytics.
9. **Response**: A response is returned to the client with the status of the request.

## Prerequisites
- Java 17 or higher
- Maven 3.8+
- Redis (for caching)
- MongoDB (for data storage)
- Kafka (optional, for streaming)
- RabbitMQ (optional, for ChatBot integration)

## Setup and Installation
1. Clone the repository:
   ```bash
   git clone <repository-url>
   ```
2. Navigate to the project directory:
   ```bash
   cd ms-sms-transmitter
   ```
3. Build the project:
   ```bash
   mvn clean install
   ```
4. Run the application:
   ```bash
   mvn spring-boot:run
   ```

   Or with a specific profile:
   ```bash
   mvn spring-boot:run -Dspring-boot.run.profiles=dev
   ```

## Configuration
The application can be configured using the `application.properties` file located in the `src/main/resources` directory. Key configuration properties include:

### Core Configuration
```properties
# Short code mappings
dxl.ms.dxl-short-codes=
dxl.ms.tibco-short-codes=100,234
dxl.ms.apache-short-codes=20444
dxl.ms.dubi-short-codes=20444
dxl.ms.zuri-short-codes=100,234

# Message configuration
dxl.ms.message-priority=5
dxl.ms.is-message-interactive=true
dxl.ms.is-message-acknowledged=false
dxl.ms.http-retries=3

# Fallback configuration
dxl.ms.max-token-refresh-retries=3
dxl.ms.apache-fallback-integrator=TIBCO
dxl.ms.token-refresh-retry-delay-ms=1000

# Integration endpoints
dxl.ms.dxl-endpoint=https://dxl.de.safaricom.co.ke/ms-tmf-dxl-notification/api/tmf/v1/communicationMessage
dxl.ms.tibco-endpoint=http://172.29.120.23:31111/auth/communicationAPI/v1/communication
dxl.ms.apache-endpoint=http://172.29.120.23:31729/sms/v1/single/notification
dxl.ms.sms-callback-url=http://10.255.172.90/ms-sms-receiver/api/v1/sender/sms/dxl

# Authentication
dxl.ms.apache-auth-endpoint=http://172.29.120.23:31228/api/v1/auth/request-token
dxl.ms.apache-auth-authorization=Basic YXBpVXNlcjo4aEYmcVBHR04kZHhWcUh0OVZ3WmtwWUg=
dxl.ms.apache-auth-username=sms-sender-user
dxl.ms.apache-auth-password=********
dxl.ms.apache-auth-realm=SMS
dxl.ms.apache-auth-client-id=ms-sms-sender-adapter
dxl.ms.apache-auth-token-key=apache_auth_token
dxl.ms.tibco-url-keyword=communicationAPI
dxl.ms.auth-token-expire-time=50

# Streaming configuration
dxl.ms.kafka-stream-enabled=false
dxl.ms.database-stream-enabled=true
dxl.ms.dubi-stream-enabled=true
dxl.ms.zuri-stream-enabled=true

# RabbitMQ configuration
pre.queue.namespace=v2-zuri-prod
dxl.ms.bot-exchange-name=${pre.queue.namespace}-environment
dxl.ms.sms-request-queue=${pre.queue.namespace}-requests-queue-sms

# Encryption
dxl.ms.encryption-key=********
dxl.ms.init-vector=********
```

### MongoDB Configuration
```properties
spring.data.mongodb.uri=mongodb+srv://zuriProd:<EMAIL>/interactive-sms_db?retryWrites=true&w=majority
```

### Redis Configuration
```properties
spring.data.redis.enabled=true
spring.data.redis.host=redis.de.safaricom.co.ke
spring.data.redis.port=7000
spring.data.redis.password=********
spring.data.redis.database=10
spring.data.redis.timeout=60000
spring.data.redis.ssl=false
```

### Kafka Configuration
```properties
dxl.kafka.enabled=false
dxl.stream.producer=kafka
dxl.kafka.bootstrap-servers=b-2.eu-west-1-aws-dxl-msk.3q7sej.c6.kafka.eu-west-1.amazonaws.com:9096,b-1.eu-west-1-aws-dxl-msk.3q7sej.c6.kafka.eu-west-1.amazonaws.com:9096
dxl.kafka.security-protocol=SASL_SSL
dxl.kafka.sasl-mechanism=SCRAM-SHA-512
```

### RabbitMQ Configuration
```properties
spring.rabbitmq.host=b-a404aec5-a4e5-4547-9b64-dc4acc6f84ca.mq.eu-west-1.amazonaws.com
spring.rabbitmq.port=5671
spring.rabbitmq.username=ZuriAdmin
spring.rabbitmq.password=********
spring.rabbitmq.ssl.enabled=true
spring.rabbitmq.ssl.algorithm=TLSv1.2
spring.rabbitmq.listener.simple.concurrency=45
spring.rabbitmq.listener.simple.max-concurrency=115
spring.rabbitmq.listener.simple.prefetch=1
```

## API Endpoints

### V1 API (Token in Path)
```
POST /api/v1/integrations/{authToken}/sendsms
```

#### Request Body
```json
{
  "shortCode": "1234",
  "customerPhoneNumber": "254712345678",
  "message": {
    "type": "text",
    "details": "Hello, this is a test message."
  }
}
```

### V2 API (Token in Header)
```
POST /api/v2/sms/send
```

#### Headers
```
x-app-id: your-auth-token
x-conversation-id: your-conversation-id
```

#### Request Body
```json
{
  "shortCode": "1234",
  "customerPhoneNumber": "254712345678",
  "message": {
    "type": "buttons",
    "title": "Please select an option:",
    "details": ["Option 1", "Option 2", "Option 3"]
  }
}
```

#### Response
```json
{
  "header": {
    "conversationId": "7fffffff-0000-0000-0000-000000000025",
    "responseCode": 202,
    "responseMessage": "Request accepted for processing",
    "timestamp": "2023-06-15T10:30:45.123Z"
  },
  "body": null
}
```

## Error Handling
The service uses a centralized error handling mechanism to log errors and return appropriate HTTP status codes:

- **400 Bad Request**: Invalid input parameters or message format
- **401 Unauthorized**: Invalid or missing authentication token
- **403 Forbidden**: Token not authorized for the specified short code
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server-side errors
- **503 Service Unavailable**: External integration service unavailable

### Error Response Example
```json
{
  "header": {
    "conversationId": "7fffffff-0000-0000-0000-000000000025",
    "responseCode": 400,
    "responseMessage": "Invalid message format: Details value contains invalid characters",
    "timestamp": "2023-06-15T10:30:45.123Z"
  },
  "body": null
}
```

## Logging and Monitoring
The service uses a structured logging approach with the following components:

- **WsLogManager**: Provides methods for logging at different levels (info, debug, warn, error)
- **Conversation ID**: All logs include the conversation ID for request tracing
- **Process Duration**: Logs include the time taken to process each request
- **Error Context**: Error logs include detailed context about what went wrong

Logs can be aggregated and analyzed using tools like ELK Stack or Splunk.

## Testing

### Unit Testing
The service includes comprehensive unit tests for key components. Run the tests using Maven:

```bash
mvn test
```

### Integration Testing
For integration testing, you can use the provided Postman collection in the `docs` directory.

```bash
# Run integration tests only
mvn test -Dtest=*IT
```

### Test Coverage
Generate a test coverage report using:

```bash
mvn clean verify
```

The HTML report will be available in the `target/site/jacoco` directory.

### Testing Strategy

The testing approach follows these principles:

1. **Unit Tests**: Test individual components in isolation with mocked dependencies
2. **Integration Tests**: Test interactions between components and external systems
3. **End-to-End Tests**: Test complete flows from API to external systems

Key areas covered by tests:
- Request validation and error handling
- Message conversion and formatting
- Routing logic based on short codes
- Integration with external systems
- Error recovery and retry mechanisms

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify that the auth token is valid and not expired
   - Check that the token is authorized for the specified short code

2. **Integration Errors**
   - Check the external API endpoints are accessible
   - Verify the authentication credentials for external APIs
   - Check the network connectivity to external services

3. **Message Format Errors**
   - Ensure the message format follows the expected structure
   - Check for invalid characters in the message content

4. **Performance Issues**
   - Monitor Redis cache hit/miss ratio
   - Check MongoDB connection pool settings
   - Verify Kafka producer configurations

5. **Circular Dependencies**
   - If you encounter circular dependency errors, check the dependency structure
   - Use the `FallbackService` for routing to alternative integration channels
   - Follow the dependency inversion principle when adding new components

## Developer Guide

### Getting Started for New Developers

1. **Environment Setup**
   - Install Java 17 and Maven 3.8+
   - Set up MongoDB and Redis locally or use the development instances
   - Configure your IDE (recommended: IntelliJ IDEA)

2. **Local Configuration**
   - Create a local `application-local.properties` file with development settings
   - Use the following command to run with your local profile:
     ```bash
     mvn spring-boot:run -Dspring-boot.run.profiles=local
     ```

3. **Mock External Services**
   - For local development, you can use Wiremock to simulate external APIs:
     ```bash
     # Start Wiremock on port 8080
     java -jar wiremock-standalone-2.27.2.jar --port 8080
     ```
   - Sample stubs are available in the `src/test/resources/wiremock` directory

### Development Workflow

1. **Branching Strategy**
   - `main` - Production-ready code
   - `develop` - Integration branch for features
   - `feature/*` - New features and enhancements
   - `bugfix/*` - Bug fixes
   - `hotfix/*` - Urgent production fixes

2. **Code Review Process**
   - Create a pull request to the `develop` branch
   - Ensure all tests pass and code meets quality standards
   - Request review from at least one team member
   - Address all comments before merging

3. **Commit Guidelines**
   - Use descriptive commit messages
   - Reference issue numbers when applicable
   - Format: `[TYPE]: Short description (#issue)`
   - Types: FEAT, FIX, DOCS, STYLE, REFACTOR, TEST, CHORE

### Contribution Guidelines

1. **Before You Start**
   - Check existing issues and pull requests
   - Discuss major changes in an issue before implementation

2. **Code Standards**
   - Follow Java coding conventions
   - Use meaningful variable and method names
   - Write comprehensive JavaDoc comments
   - Maintain test coverage for new code

3. **Pull Request Process**
   - Fork the repository
   - Create a new branch from `develop`:
     ```bash
     git checkout -b feature/your-feature-name
     ```
   - Make your changes and commit them:
     ```bash
     git commit -m "FEAT: Add new feature (#123)"
     ```
   - Push to your fork:
     ```bash
     git push origin feature/your-feature-name
     ```
   - Open a pull request to the `develop` branch
   - Fill out the pull request template completely

## Environment Configuration

### Available Environments

| Environment | Description                   | Config Server Profile |
|-------------|-------------------------------|------------------------|
| DEV         | Development environment       | development            |
| PROD        | Production environment        | production             |

### Environment-Specific Configuration

The service uses Spring Cloud Config Server to manage environment-specific configurations. The config server URL is specified in the `application.properties` file:

```properties
spring.config.import=optional:configserver:http://ms-config-server-service.dxl.svc.cluster.local:8888
```

To run the application with a specific profile:

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=development
```

## Monitoring and Observability

### Logging

The service uses structured logging with the following pattern:

```
%d{yyyy-MM-dd HH:mm:ss.SSS} | Severity=%-5p | MicroService=${spring.service.name} | %m %n
```

Logs can be viewed in the following ways:

1. **Local Development**: Console output
2. **Kubernetes**: Using kubectl logs
   ```bash
   kubectl logs -f <pod-name> -n <namespace>
   ```
3. **ELK Stack**: Centralized logging platform
   - URL(Dev): https://search-digitalengineering-es-uat-vrx4sv6dhsgujucde4esnmlpoi.eu-west-1.es.amazonaws.com/_plugin/kibana/app/home#/
   - URL(Prod): https://search-digitalengineering-es-prd-3h3frco4wxmeckldlsugazgmvy.eu-west-1.es.amazonaws.com/_plugin/kibana/app/home#/
   - Index pattern: `ms-sms-transmitter-*`

### Metrics

The service collects the following metrics:

1. **SMS Delivery Metrics**:
   - Success/failure rates
   - Response times
   - Volume by short code

### Health Checks

The service exposes health endpoints via Spring Boot Actuator:

```
GET /actuator/health
```

This endpoint returns the overall health status of the service and its dependencies (MongoDB, Redis, RabbitMQ).

## License
This project is licensed under the MIT License. See the LICENSE file for details.
