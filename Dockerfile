FROM 559104660845.dkr.ecr.eu-west-1.amazonaws.com/amazoncorretto:17-alpine3.20-jdk
LABEL maintainer="<EMAIL>"
EXPOSE 9600
VOLUME /tmp
ADD target/ms-sms-transmitter-1.0.1.jar ms-sms-transmitter.jar
RUN /bin/sh -c 'touch /ms-sms-transmitter.jar'
ENV TZ=Africa/Nairobi
ENV NO_PROXY="*.safaricom.net,*.safaricom.co.ke,*.kubernetesuat.*"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
ENTRYPOINT ["java","-Xmx256m", "-XX:+UseG1GC", "-Djava.security.egd=file:/dev/./urandom","-jar","/ms-sms-transmitter.jar"]