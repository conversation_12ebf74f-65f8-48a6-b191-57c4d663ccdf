package com.safaricom.dxl.sms.transmitter.exception;

import lombok.Generated;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class BadRequestException extends RuntimeException {
    private final String code;

    public BadRequestException(String message, String code) {
        super(message);
        this.code = code;
    }

    @Generated
    public String getCode() {
        return this.code;
    }
}