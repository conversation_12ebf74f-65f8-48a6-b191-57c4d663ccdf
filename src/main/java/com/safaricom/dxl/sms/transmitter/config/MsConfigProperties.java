package com.safaricom.dxl.sms.transmitter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for the SMS Transmitter microservice.
 * <p>
 * This class centralizes all configuration properties used throughout the application,
 * loaded from application.properties/yml files with the prefix "dxl.ms".
 * <p>
 * Properties include endpoints for different integration channels, authentication details,
 * short code mappings, message handling configurations, and feature flags.
 *
 * <AUTHOR>
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "dxl.ms")
public class MsConfigProperties {
    /** RabbitMQ exchange name for chatbot messages */
    private String botExchangeName;

    /** RabbitMQ queue name for SMS requests */
    private String smsRequestQueue;

    /** Comma-separated list of short codes routed to DXL integration */
    private String dxlShortCodes;

    /** Comma-separated list of short codes routed to Tibco integration */
    private String tibcoShortCodes;

    /** Comma-separated list of short codes routed to Apache integration */
    private String apacheShortCodes;

    /** Comma-separated list of short codes for Dubi platform */
    private String dubiShortCodes;

    /** Comma-separated list of short codes for Zuri platform */
    private String zuriShortCodes;

    /** Message priority level for SMS delivery */
    private String messagePriority;

    /** Flag indicating if messages should be interactive */
    private boolean isMessageInteractive;

    /** Flag indicating if message delivery acknowledgment is required */
    private boolean isMessageAcknowledged;

    /** Number of HTTP retry attempts for failed requests */
    private short httpRetries;

    /** Callback URL for SMS delivery status notifications */
    private String smsCallbackUrl;

    /** Endpoint URL for DXL integration */
    private String dxlEndpoint;
    private String dxlAuth;
    private String dxlApiKey;

    /** Endpoint URL for Tibco integration */
    private String tibcoEndpoint;

    /** Endpoint URL for Apache integration */
    private String apacheEndpoint;

    /** Authentication endpoint URL for Apache integration */
    private String apacheAuthEndpoint;

    /** Authorization header value for Apache integration authentication */
    private String apacheAuthAuthorization;

    /** Username for Apache integration authentication */
    private String apacheAuthUsername;

    /** Password for Apache integration authentication */
    private String apacheAuthPassword;

    /** Realm for Apache integration authentication */
    private String apacheAuthRealm;

    /** Client ID for Apache integration authentication */
    private String apacheAuthClientId;

    /** Token key name for Apache integration authentication */
    private String apacheAuthTokenKey;

    /** Keyword used in Tibco URL construction */
    private String tibcoUrlKeyword;

    /** Authentication token expiration time in milliseconds */
    private long authTokenExpireTime;

    /** Flag indicating if Kafka streaming is enabled */
    private boolean kafkaStreamEnabled;

    /** Flag indicating if database streaming is enabled */
    private boolean databaseStreamEnabled;

    /** Flag indicating if Dubi platform streaming is enabled */
    private boolean dubiStreamEnabled;

    /** Flag indicating if Zuri platform streaming is enabled */
    private boolean zuriStreamEnabled;

    /** Application profile (e.g., dev, test, prod) */
    private String profile;

    /** Encryption key for sensitive data */
    private String encryptionKey;

    /** Initialization vector for encryption */
    private String initVector;

    /** Flag indicating if authentication token caching is enabled */
    private boolean cacheAuthTokenEnabled;

    /** Maximum number of retry attempts for token refresh */
    private int maxTokenRefreshRetries = 3;

    /** Fallback integrator to use when Apache integration fails (TIBCO, DXL, or NONE) */
    private String apacheFallbackIntegrator = "NONE";

    /** Delay between token refresh retries in milliseconds */
    private long tokenRefreshRetryDelayMs = 1000;
}