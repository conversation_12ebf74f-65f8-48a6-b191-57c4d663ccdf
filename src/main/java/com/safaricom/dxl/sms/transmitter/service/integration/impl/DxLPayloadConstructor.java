package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.dxl.DxlSmsPayload;
import com.safaricom.dxl.sms.transmitter.model.pojo.dxl.SenderReceiverDetails;
import com.safaricom.dxl.sms.transmitter.service.integration.IPayloadConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class DxLPayloadConstructor implements IPayloadConstructor<DxlSmsPayload> {
    private final MsConfigProperties properties;

    @Override
    public DxlSmsPayload generatePayload(String shortCode, String destinationAddress, String message, Map<String, String> headers) {
        List<SenderReceiverDetails> receiver = new ArrayList<>();
        SenderReceiverDetails senderReceiverDetails = SenderReceiverDetails.builder()
                .name("")
                .phoneNumber(destinationAddress)
                .build();
        receiver.add(senderReceiverDetails);

        SenderReceiverDetails sender = SenderReceiverDetails.builder()
                .name("SMS".concat(shortCode))
                .phoneNumber(shortCode)
                .build();

        return DxlSmsPayload.builder()
                .sender(sender)
                .receiver(receiver)
                .content(message)
                .messageType("sms")
                .type("sms")
                .priority(properties.getMessagePriority())
                .interactive(String.valueOf(properties.isMessageInteractive()))
                .callBackUrl(properties.getSmsCallbackUrl())
                .build();
    }
}
