package com.safaricom.dxl.sms.transmitter.exception;

import lombok.Generated;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when a request is unauthorized.
 * This includes invalid authentication tokens or insufficient permissions.
 */
@ResponseStatus(HttpStatus.UNAUTHORIZED)
public class WsUnauthorizedException extends RuntimeException {
    private final String code;

    /**
     * Constructs a new WsUnauthorizedException with the specified detail message.
     *
     * @param message the detail message
     */
    public WsUnauthorizedException(String message) {
        super(message);
        this.code = "401";
    }

    /**
     * Constructs a new WsUnauthorizedException with the specified detail message and error code.
     *
     * @param message the detail message
     * @param code the error code
     */
    public WsUnauthorizedException(String message, String code) {
        super(message);
        this.code = code;
    }

    /**
     * Returns the error code associated with this exception.
     *
     * @return the error code
     */
    @Generated
    public String getCode() {
        return this.code;
    }
}