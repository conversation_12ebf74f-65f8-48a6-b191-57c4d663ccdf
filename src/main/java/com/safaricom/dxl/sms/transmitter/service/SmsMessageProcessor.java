package com.safaricom.dxl.sms.transmitter.service;

import com.safaricom.dxl.sms.transmitter.exception.BadRequestException;
import com.safaricom.dxl.sms.transmitter.exception.InternalServerErrorException;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.model.pojo.MessageFormat;
import com.safaricom.dxl.sms.transmitter.service.processor.IApiProcessor;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;


/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SmsMessageProcessor {

    public static final String PROCESS_SMS_MESSAGE = "processSmsMessage";
    private final WsResponseMapper responseMapper;
    private final Utilities utilities;
    private final List<IApiProcessor> apiProcessors;
    private final MessageConversionService messageConversionService;

    /**
     * Processes a textual request by converting it to the appropriate format and routing it to the correct processor.
     *
     * @param shortCode the short code of the integration
     * @param customerPhoneNumber the phone number of the customer
     * @param messagePayload the message payload to process
     * @param headers the request headers
     * @param requestReceivedTime the time the request was received
     * @return a Mono emitting the response after processing the request
     */
    public Mono<WsResponse> processSmsMessage(String shortCode, String customerPhoneNumber, Message messagePayload, Map<String, String> headers, LocalDateTime requestReceivedTime) {
        WsLogManager.starterDebug(shortCode, PROCESS_SMS_MESSAGE, customerPhoneNumber, "Received");

        // Validate input parameters
        if (shortCode == null || shortCode.trim().isEmpty()) {
            return Mono.error(new BadRequestException("Short code cannot be null or empty", ERR_BAD_REQUEST));
        }

        if (customerPhoneNumber == null || customerPhoneNumber.trim().isEmpty()) {
            return Mono.error(new BadRequestException("Customer phone number cannot be null or empty", ERR_BAD_REQUEST));
        }

        if (messagePayload == null) {
            return Mono.error(new BadRequestException("Message payload cannot be null", ERR_BAD_REQUEST));
        }

        if (messagePayload.getType() == null) {
            return Mono.error(new BadRequestException("Message type cannot be null", ERR_BAD_REQUEST));
        }

        try {
            // Format the message
            MessageFormat formattedText = messageConversionService.prepareMessageFormat(messagePayload, headers, requestReceivedTime);

            // Process the formatted message
            return messageConversionService.convertMenuToTextFormat(formattedText.getHeader(), shortCode, formattedText.getMessage(), customerPhoneNumber, headers, requestReceivedTime)
                    .flatMap(message -> routeRequest(shortCode, customerPhoneNumber, message, requestReceivedTime, headers)
                    )
                    .onErrorResume(error -> {
                        // Handle specific error types differently
                        if (error instanceof BadRequestException) {
                            return Mono.error(error); // Propagate BadRequestException
                        } else if (error instanceof InternalServerErrorException) {
                            return handleErrorGracefully(shortCode, customerPhoneNumber, headers, requestReceivedTime, error.getMessage());
                        } else {
                            String errorMsg = "Error processing message for customer " + customerPhoneNumber + ": " + error.getMessage();
                            return handleErrorGracefully(shortCode, customerPhoneNumber, headers, requestReceivedTime, errorMsg);
                        }
                    });
        } catch (Exception e) {
            // Catch any exceptions thrown during message formatting
            String errorMsg = "Unexpected error processing request for customer " + customerPhoneNumber + ": " + e.getMessage();
            WsLogManager.starterError(headers.getOrDefault(X_CONVERSATION_ID, ES), PROCESS_SMS_MESSAGE, "0ms", errorMsg);
            return handleErrorGracefully(shortCode, customerPhoneNumber, headers, requestReceivedTime, errorMsg);
        }
    }

    /**
     * Handles errors gracefully by logging them and sending a default error response.
     *
     * @param shortCode the short code of the integration
     * @param customerPhoneNumber the phone number of the customer
     * @param headers the request headers
     * @param requestReceivedTime the time the request was received
     * @param errorMessage the error message to log
     * @return a Mono emitting the default error response
     */
    private Mono<WsResponse> handleErrorGracefully(String shortCode, String customerPhoneNumber, Map<String, String> headers, LocalDateTime requestReceivedTime, String errorMessage) {
        WsLogManager.starterError(headers.getOrDefault(X_CONVERSATION_ID, ES), PROCESS_SMS_MESSAGE, utilities.calculateProcessingTime(requestReceivedTime), errorMessage);
        return routeRequest(shortCode, customerPhoneNumber, DEFAULT_ERR_RESP, requestReceivedTime, headers);
    }

    /**
     * Routes the request to the appropriate API processor based on the source address.
     *
     * @param sourceAddress      the source address of the request
     * @param destinationAddress the destination address of the request
     * @param message            the message to be processed
     * @param requestReceivedTime the time the request was received
     * @param headers            the headers of the request
     * @return a Mono emitting the WsResponse after processing the request
     */
    public Mono<WsResponse> routeRequest(String sourceAddress, String destinationAddress, String message, LocalDateTime requestReceivedTime, Map<String, String> headers) {
        return apiProcessors.stream()
                .filter(apiProcessor -> apiProcessor.supports(sourceAddress))
                .findFirst()
                .map(apiProcessor -> apiProcessor.process(sourceAddress, destinationAddress, message, requestReceivedTime, headers))
                .orElseGet(() -> responseMapper.setApiResponse(ERR_SERVER_ERROR, NULL, TRANS_ROUTE_REQUEST, FALSE, headers));
    }
}