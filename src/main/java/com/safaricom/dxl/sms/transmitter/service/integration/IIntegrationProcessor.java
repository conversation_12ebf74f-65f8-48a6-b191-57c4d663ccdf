package com.safaricom.dxl.sms.transmitter.service.integration;

import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Interface defining the contract for integration processors that handle SMS transmission
 * to external systems like Tibco, Apache, and DXL.
 * <p>
 * Integration processors are responsible for:
 * <ul>
 *   <li>Constructing the appropriate payload for the external system</li>
 *   <li>Setting up the required headers for authentication and routing</li>
 *   <li>Making the actual HTTP/API call to the external system</li>
 *   <li>Processing the response from the external system</li>
 *   <li>Handling errors and retries</li>
 * </ul>
 * <p>
 * Each integration channel (Tibco, Apache, DXL) has its own implementation of this interface
 * to handle the specific requirements of that channel.
 */
public interface IIntegrationProcessor {

    /**
     * Processes an SMS transmission request for a specific integration channel.
     * <p>
     * This method handles the entire integration process, from constructing the payload
     * and headers to making the API call and processing the response. It encapsulates all
     * the channel-specific logic required to send an SMS through the external system.
     *
     * @param sourceAddress The source address (short code) of the SMS
     * @param destinationAddress The destination phone number
     * @param message The message content to be sent
     * @param reqReceiveTime The time when the original request was received
     * @param headers HTTP headers from the original request
     * @return A Mono emitting a WsResponse with the result of the integration
     */
    Mono<WsResponse> processIntegrationRequest(String sourceAddress, String destinationAddress, String message, LocalDateTime reqReceiveTime, Map<String, String> headers);
}
