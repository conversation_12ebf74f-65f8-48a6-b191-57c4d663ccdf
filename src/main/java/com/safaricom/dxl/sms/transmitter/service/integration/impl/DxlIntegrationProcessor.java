package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.dxl.DxlSmsPayload;
import com.safaricom.dxl.sms.transmitter.service.integration.AbstractIntegrationProcessor;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_INTEGRATION_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.TRANS_SEND_DXL_SMS;

/**
 * Integration processor for the DXL SMS gateway.
 * <p>
 * This processor handles sending SMS messages through the DXL gateway,
 * which is one of the supported integration channels.
 */
@Service
public class DxlIntegrationProcessor extends AbstractIntegrationProcessor<DxlSmsPayload, WsResponse> {
    private final DxLPayloadConstructor payloadConstructor;
    private final DxlHeaderConstructor headerConstructor;
    private final MsConfigProperties properties;
    private final DxlApiRespProcessor apiRespProcessor;
    private final Utilities utilities;

  public DxlIntegrationProcessor(
            DxLPayloadConstructor payloadConstructor,
            DxlHeaderConstructor headerConstructor,
            MsConfigProperties properties,
            HttpRequestHandler httpRequestHandler,
            DxlApiRespProcessor apiRespProcessor,
            Utilities utilities) {
        super(httpRequestHandler);
        this.payloadConstructor = payloadConstructor;
        this.headerConstructor = headerConstructor;
        this.properties = properties;
        this.apiRespProcessor = apiRespProcessor;
        this.utilities = utilities;
    }

    @Override
    protected DxlSmsPayload generatePayload(String sourceAddress, String destinationAddress, String message, Map<String, String> headers) {
        return payloadConstructor.generatePayload(sourceAddress, destinationAddress, message, headers);
    }

    @Override
    protected String getEndpoint() {
        return properties.getDxlEndpoint();
    }

    @Override
    protected Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
        return headerConstructor.constructHeaders(destinationAddress, headers);
    }

    @Override
    protected Class<WsResponse> getResponseClass() {
        return WsResponse.class;
    }

    @Override
    protected Mono<WsResponse> processResponse(WsResponse response, String sourceAddress, String destinationAddress, String endpoint, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endpoint, message, reqReceiveTime, headers);
    }

    @Override
    protected Mono<WsResponse> handleError(Throwable error, String sourceAddress, String destinationAddress, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return utilities.handleExceptions(headers, reqReceiveTime, ERR_INTEGRATION_ERROR, "Dxl API Call", TRANS_SEND_DXL_SMS, error, 500);
    }
}
