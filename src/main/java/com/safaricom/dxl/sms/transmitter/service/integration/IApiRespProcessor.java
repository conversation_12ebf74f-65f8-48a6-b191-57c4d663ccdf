package com.safaricom.dxl.sms.transmitter.service.integration;

import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

public interface IApiRespProcessor<T> {

    Mono<WsResponse> processApiResponse(T response, String sourceAddress, String destinationAddress, String endPoint, String message, LocalDateTime reqReceiveTime, Map<String, String> headers);
}
