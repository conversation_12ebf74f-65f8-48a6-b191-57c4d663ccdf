package com.safaricom.dxl.sms.transmitter.model.pojo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Represents a message to be sent via SMS.
 * <p>
 * The message can be of type "text" or "buttons". For "text" type, the details should be a String.
 * For "buttons" type, the details should be a List of options and title is required.
 */
@Getter
@Setter
@NoArgsConstructor
public class Message {
    @NotBlank(message = "Message type is required")
    @Pattern(regexp = "(?i)^(buttons|text)$", message = "Message type must be either 'buttons' or 'text'")
    private String type;

    private Object details;

    @Size(max = 160, message = "Message title cannot exceed 160 characters")
    @Pattern(regexp = "^[\\p{Alnum}\\s-_@:.,]*$", message = "Message title contains invalid characters")
    private String title;
}
