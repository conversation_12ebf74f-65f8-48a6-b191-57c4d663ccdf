package com.safaricom.dxl.sms.transmitter.datalayer.dao;

import com.safaricom.dxl.sms.transmitter.datalayer.entities.MenuForUser;
import reactor.core.publisher.Mono;

public interface IMenuForUserAo {
    Mono<MenuForUser> add(MenuForUser menuForUser);

    Mono<MenuForUser> update(MenuForUser menuForUser);

    Mono<MenuForUser> getMenuByUserId(String userId);

    Mono<MenuForUser> getMenuByUserIdAndShortCode(String userId, String shortCode);
}
