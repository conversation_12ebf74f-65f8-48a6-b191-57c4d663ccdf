package com.safaricom.dxl.sms.transmitter.service;

import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Service responsible for routing to fallback integrators when the primary integration fails.
 * <p>
 * This service provides methods to route SMS requests to alternative integration channels
 * based on configuration. It supports fallback to TIBCO, DXL, or no fallback.
 */
public interface FallbackService {
    
    /**
     * Routes an SMS request to the specified fallback integrator.
     *
     * @param fallbackType The type of fallback integrator to use (TIBCO, DXL, or NONE)
     * @param sourceAddress The source address (short code)
     * @param destinationAddress The destination address (phone number)
     * @param message The message to send
     * @param requestTime The time the request was received
     * @param headers The request headers
     * @return A Mono emitting the response from the fallback integrator
     */
    Mono<WsResponse> routeToFallbackIntegrator(String fallbackType, String sourceAddress, 
                                              String destinationAddress, String message, 
                                              LocalDateTime requestTime, Map<String, String> headers);
    
    /**
     * Routes an SMS request to the TIBCO integration.
     *
     * @param sourceAddress The source address (short code)
     * @param destinationAddress The destination address (phone number)
     * @param message The message to send
     * @param requestTime The time the request was received
     * @param headers The request headers
     * @return A Mono emitting the response from the TIBCO integration
     */
    Mono<WsResponse> routeToTibcoIntegration(String sourceAddress, String destinationAddress, 
                                            String message, LocalDateTime requestTime, 
                                            Map<String, String> headers);
    
    /**
     * Routes an SMS request to the DXL integration.
     *
     * @param sourceAddress The source address (short code)
     * @param destinationAddress The destination address (phone number)
     * @param message The message to send
     * @param requestTime The time the request was received
     * @param headers The request headers
     * @return A Mono emitting the response from the DXL integration
     */
    Mono<WsResponse> routeToDxlIntegration(String sourceAddress, String destinationAddress, 
                                          String message, LocalDateTime requestTime, 
                                          Map<String, String> headers);
}
