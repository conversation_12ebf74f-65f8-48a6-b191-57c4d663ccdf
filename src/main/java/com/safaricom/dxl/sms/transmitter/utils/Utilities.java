package com.safaricom.dxl.sms.transmitter.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.exception.BadRequestException;
import com.safaricom.dxl.sms.transmitter.exception.IntegrationException;
import com.safaricom.dxl.sms.transmitter.exception.InternalServerErrorException;
import com.safaricom.dxl.sms.transmitter.exception.WsUnauthorizedException;
import com.safaricom.dxl.webflux.starter.enums.WsProcessLogger;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_SERVER_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.LOCAL_TIME_ZONE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Component
@RequiredArgsConstructor
public class Utilities {
    public final WebClient webClient;
    private final WsStarterService helper;
    private final MsConfigProperties properties;
    private final WsResponseMapper responseMapper;

    public Map<String, String> createHeadersWithDefaults(String trackingId, String msisdn, String sourceSystem) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, CONTENT_TYPE);
        headers.put(HttpHeaders.ACCEPT_ENCODING, MediaType.APPLICATION_JSON_VALUE);
        headers.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        headers.put(X_CONVERSATION_ID, trackingId);
        headers.put(X_MESSAGE_ID, trackingId);
        headers.put("x-api-key", properties.getDxlApiKey());
        headers.put(X_SOURCE_SYSTEM, sourceSystem);
        headers.put(X_APP, sourceSystem);
        headers.put(X_VERSION, "1.0");
        headers.put(X_DEVICE_TOKEN, trackingId);
        headers.put(X_MSISDN, msisdn);
        headers.put(HEADER_AUTHORIZATION, properties.getDxlAuth());
        return headers;
    }

    /**
     * Serializes an object to JSON string.
     *
     * @param object the object to serialize
     * @return the JSON string representation of the object, or null if serialization fails
     */
    public static String serializeToJson(Object object) {
        if (object == null) {
            WsProcessLogger.ERROR.log("Cannot serialize null object");
            return null;
        }

        try {
            var objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            WsProcessLogger.ERROR.log("Error serializing object to JSON | " + e.getMessage());
            return null;
        }
    }

    /**
     * Deserializes a JSON string to an object of the specified class.
     *
     * @param json the JSON string to deserialize
     * @param clazz the class of the object to deserialize to
     * @param <T> the type of the object to deserialize to
     * @return the deserialized object, or null if deserialization fails
     */
    public static <T> T deserializeFromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            WsProcessLogger.ERROR.log("Cannot deserialize null or empty JSON string");
            return null;
        }

        if (clazz == null) {
            WsProcessLogger.ERROR.log("Cannot deserialize to null class");
            return null;
        }

        try {
            var objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            WsProcessLogger.ERROR.log("Error deserializing JSON to object | " + e.getMessage());
            return null;
        }
    }

    public boolean isShortCodeMappedToDxl(String smsShortCode) {
        List<String> dxlShortCodeList = helper.getList(properties.getDxlShortCodes(), ",");
        return dxlShortCodeList.contains(smsShortCode);
    }

    public boolean isShortCodeMappedToTibco(String smsShortCode) {
        List<String> tibcoOldShortCodeList = helper.getList(properties.getTibcoShortCodes(), ",");
        return tibcoOldShortCodeList.contains(smsShortCode);
    }

    public boolean isShortCodeMappedToApache(String smsShortCode) {
        List<String> tibcoNewShortCodeList = helper.getList(properties.getApacheShortCodes(), ",");
        return tibcoNewShortCodeList.contains(smsShortCode);
    }

    public boolean isShortCodeMappedToZuri(String smsShortCode) {
        List<String> zuriCodeList = helper.getList(properties.getZuriShortCodes(), ",");
        return zuriCodeList.contains(smsShortCode);
    }

    public boolean isShortCodeMappedToDubi(String smsShortCode) {
        List<String> dubiCodeList = helper.getList(properties.getDubiShortCodes(), ",");
        return dubiCodeList.contains(smsShortCode);
    }

    /**
     * Handles an error by logging it and creating an appropriate API response.
     * This method is used for synchronous error handling.
     *
     * @param headers the request headers
     * @param reqReceiveTime the time the request was received
     * @param process the process where the error occurred
     * @param params the parameters for the API response
     * @param throwable the error that occurred
     */
    public void handleError(Map<String, String> headers, LocalDateTime reqReceiveTime, String process, String params, Throwable throwable) {
        String errorMessage = throwable.getMessage() != null ? throwable.getMessage() : "Unknown error";
        long responseTime = Duration.between(reqReceiveTime, LocalDateTime.now()).toMillis();

        // Log the error with context information
        WsLogManager.starterError(
                headers.getOrDefault(X_CONVERSATION_ID, ES),
                process,
                String.valueOf(responseTime),
                "Error in " + process + ": " + errorMessage
        );

        // Create an API response
        responseMapper.setApiResponse(ERR_SERVER_ERROR, NULL, params, false, headers);
    }

    /**
     * Handles an exception by logging it and returning an appropriate API response.
     * This method is used for reactive error handling.
     *
     * @param headers the request headers
     * @param reqReceiveTime the time the request was received
     * @param errorCode the error code to include in the response
     * @param process the process where the error occurred
     * @param params the parameters for the API response
     * @param throwable the error that occurred
     * @param severity the severity level of the error (200 for info, 400 for warning, 500 for error)
     * @return a Mono emitting the error response
     */
    public Mono<WsResponse> handleExceptions(Map<String, String> headers, LocalDateTime reqReceiveTime, String errorCode, String process, String params, Throwable throwable, int severity) {
        String errorMessage = throwable.getMessage() != null ? throwable.getMessage() : "Unknown error";
        String errorContext = "Error in " + process + ": " + errorMessage;

        // Log the error with context information
        logMessage(severity, process, errorContext, "", headers, reqReceiveTime, severity);

        // For specific exception types, we might want to propagate them instead of wrapping them
        if (throwable instanceof BadRequestException || throwable instanceof WsUnauthorizedException ||
            throwable instanceof InternalServerErrorException || throwable instanceof IntegrationException) {
            return Mono.error(throwable);
        }

        // Map the error to an appropriate exception type based on severity
        if (severity == 400) {
            return Mono.error(new BadRequestException(errorMessage, errorCode));
        } else if (severity == 500) {
            return Mono.error(new InternalServerErrorException(errorMessage, errorCode));
        }

        // Create a generic API response for other cases
        return responseMapper.setApiResponse(errorCode, NULL, params, false, headers);
    }

    /**
     * Logs a message with the appropriate log level based on the error type.
     *
     * @param httpCode the HTTP status code
     * @param process the process where the message originated
     * @param message the message to log
     * @param targetApp the target application
     * @param headers the request headers
     * @param time the time the request was received
     * @param errorType the type of error (200 for info, 400 for warning, 500 for error)
     */
    public void logMessage(int httpCode, String process, String message, String targetApp, Map<String, String> headers, LocalDateTime time, int errorType) {
        String conversationId = headers.getOrDefault(X_CONVERSATION_ID, ES);
        String sourceSystem = headers.getOrDefault(X_SOURCE_SYSTEM, ES);
        String duration = this.calculateProcessingTime(time);
        String safeMessage = message != null ? message : "No message provided";
        String safeProcess = process != null ? process : "Unknown process";
        String safeTargetApp = targetApp != null ? targetApp : ES;

        switch (errorType) {
            case 200:
                WsLogManager.info(conversationId, ES, safeProcess, duration, ES, ES, sourceSystem, safeTargetApp, ES, httpCode, safeMessage, ES, ES, ES);
                break;
            case 400:
                WsLogManager.warn(conversationId, ES, safeProcess, duration, ES, ES, sourceSystem, safeTargetApp, ES, httpCode, safeMessage, ES, ES, ES);
                break;
            case 500:
                WsLogManager.error(conversationId, ES, safeProcess, duration, ES, ES, sourceSystem, safeTargetApp, ES, httpCode, safeMessage, ES, ES, ES);
                break;
            default:
                // Default to info level for unknown error types
                WsLogManager.info(conversationId, ES, safeProcess, duration, ES, ES, sourceSystem, safeTargetApp, ES, httpCode, safeMessage, ES, ES, ES);
                break;
        }
    }

    /**
     * Calculates the duration between the start time and the current time.
     *
     * @param startTime the start time
     * @return the duration in milliseconds as a string with "ms" suffix
     */
    public String calculateProcessingTime(LocalDateTime startTime) {
        if (startTime == null) {
            return "0ms";
        }
        return Duration.between(startTime, LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE))).toMillis() + "ms";
    }


}
