package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.datalayer.dao.ICacheAo;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.ApachePayload;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Response;
import com.safaricom.dxl.sms.transmitter.service.FallbackService;
import com.safaricom.dxl.sms.transmitter.service.StreamingService;
import com.safaricom.dxl.sms.transmitter.service.integration.AbstractIntegrationProcessor;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_INTEGRATION_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.TRANS_SEND_APACHE_SMS;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;

/**
 * Integration processor for the Apache SMS gateway.
 * <p>
 * This processor handles sending SMS messages through the Apache gateway,
 * which is one of the supported integration channels. It includes special
 * handling for authentication token expiration and retry logic.
 */
@Service
public class ApacheIntegrationProcessor extends AbstractIntegrationProcessor<ApachePayload, Response> {
    private final ApachePayloadConstructor payloadConstructor;
    private final ApacheHeaderConstructor headerConstructor;
    private final MsConfigProperties properties;
    private final TibcoApacheApiRespProcessor apiRespProcessor;
    private final Utilities utilities;
    private final StreamingService streamingService;
    private final ICacheAo cacheAo;
    private final FallbackService fallbackService;

    public ApacheIntegrationProcessor(
            ApachePayloadConstructor payloadConstructor,
            ApacheHeaderConstructor headerConstructor,
            MsConfigProperties properties,
            HttpRequestHandler httpRequestHandler,
            TibcoApacheApiRespProcessor apiRespProcessor,
            Utilities utilities,
            FallbackService fallbackService,
            StreamingService streamingService,
            ICacheAo cacheAo) {
        super(httpRequestHandler);
        this.payloadConstructor = payloadConstructor;
        this.headerConstructor = headerConstructor;
        this.properties = properties;
        this.apiRespProcessor = apiRespProcessor;
        this.utilities = utilities;
        this.fallbackService = fallbackService;
        this.streamingService = streamingService;
        this.cacheAo = cacheAo;
    }

    @Override
    protected ApachePayload generatePayload(String sourceAddress, String destinationAddress, String message, Map<String, String> headers) {
        return payloadConstructor.generatePayload(sourceAddress, destinationAddress, message, headers);
    }

    @Override
    protected String getEndpoint() {
        return properties.getApacheEndpoint();
    }

    @Override
    protected Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
        return headerConstructor.constructHeaders(destinationAddress, headers);
    }

    @Override
    protected Class<Response> getResponseClass() {
        return Response.class;
    }

    @Override
    protected Mono<WsResponse> processResponse(Response response, String sourceAddress, String destinationAddress, String endpoint, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endpoint, message, reqReceiveTime, headers);
    }

    @Override
    protected Mono<WsResponse> handleError(Throwable error, String sourceAddress, String destinationAddress, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return handleError(error, sourceAddress, destinationAddress, message, reqReceiveTime, headers, 0);
    }

    /**
     * Enhanced error handler with retry count tracking to prevent infinite loops.
     * <p>
     * This method extends the standard error handling with:
     * 1. Retry limits for authentication errors
     * 2. Exponential backoff between retries
     * 3. Fallback to alternative integration channel when retries are exhausted
     *
     * @param error The error that occurred
     * @param sourceAddress The source address (short code)
     * @param destinationAddress The destination phone number
     * @param message The message content
     * @param reqReceiveTime The time the request was received
     * @param headers The request headers
     * @param retryCount Current retry attempt count
     * @return A Mono emitting the WsResponse
     */
    protected Mono<WsResponse> handleError(Throwable error, String sourceAddress, String destinationAddress,
                                         String message, LocalDateTime reqReceiveTime, Map<String, String> headers,
                                         int retryCount) {
        // Check if this is an authentication error (401 or InvalidBearerToken message)
        boolean isAuthError = (error instanceof ResponseStatusException respExp && respExp.getStatusCode().value() == 401) ||
                             (error.getMessage() != null && error.getMessage().contains("InvalidBearerToken"));

        if (isAuthError) {
            // Check if we've exceeded the maximum retry attempts
            if (retryCount >= properties.getMaxTokenRefreshRetries()) {
                WsLogManager.starterError(headers.get(X_CONVERSATION_ID), "Fetch Token", "1s",
                                        "Max retries reached ("+retryCount+"). Attempting fallback.");

                // Stream the error for metrics
                return streamingService.streamSentSms(sourceAddress, destinationAddress, message, "401",
                                                   "Max token refresh retries exceeded: " + error.getMessage(),
                                                   reqReceiveTime, headers)
                        .then(tryFallbackIntegration(sourceAddress, destinationAddress, message, reqReceiveTime, headers));
            }

            // Log the retry attempt
            WsLogManager.starterWarn(headers.get(X_CONVERSATION_ID), "Fetch Token", "1s",
                                   "Token Expired, retrying! Attempt " + (retryCount + 1) +
                                   " of " + properties.getMaxTokenRefreshRetries());

            // Stream the error for metrics
            return streamingService.streamSentSms(sourceAddress, destinationAddress, message, "401",
                                               error.getMessage(), reqReceiveTime, headers)
                    .then(clearCacheAndRetry(sourceAddress, destinationAddress, message, reqReceiveTime, headers, retryCount + 1));
        }

        // For non-authentication errors, proceed with standard error handling
        String errorMessage = error.getMessage() != null ? error.getMessage() : "Unknown error";
        return streamingService.streamSentSms(sourceAddress, destinationAddress, message, "500",
                                           errorMessage, reqReceiveTime, headers)
                .then(utilities.handleExceptions(headers, reqReceiveTime, ERR_INTEGRATION_ERROR,
                                              "SMS API Call", TRANS_SEND_APACHE_SMS, error, 500));
    }

    /**
     * Clears the authentication token cache and retries the request with exponential backoff.
     * <p>
     * This method is called when an authentication error occurs, indicating that
     * the token has expired and needs to be refreshed. It includes exponential backoff
     * to prevent overwhelming the authentication service.
     *
     * @param sourceAddress      The source address (short code)
     * @param destinationAddress The destination phone number
     * @param message            The message content
     * @param reqReceiveTime     The time the request was received
     * @param headers            The request headers
     * @param retryCount         The current retry attempt count
     * @return A Mono emitting the WsResponse
     */
    private Mono<WsResponse> clearCacheAndRetry(String sourceAddress, String destinationAddress, String message,
                                              LocalDateTime reqReceiveTime, Map<String, String> headers, int retryCount) {
        // Calculate exponential backoff delay (base delay * 2^retryCount)
        // For example, with base delay of 1000ms: 1s, 2s, 4s, 8s, etc.
        long delayMillis = properties.getTokenRefreshRetryDelayMs() * (long) Math.pow(2, (retryCount - 1));

        WsLogManager.starterInfo(headers.get(X_CONVERSATION_ID), "Token Refresh", "0ms",
                               "Waiting " + delayMillis + "ms before retry attempt " + retryCount);

        return cacheAo.deleteCache(properties.getApacheAuthTokenKey())
                // Add delay with exponential backoff
                .then(Mono.delay(Duration.ofMillis(delayMillis)))
                // Then retry the request
                .then(processIntegrationRequest(sourceAddress, destinationAddress, message, reqReceiveTime, headers));
    }

    /**
     * Attempts to send the SMS using a fallback integration channel when Apache integration fails.
     * <p>
     * This method is called when all retry attempts for Apache integration have been exhausted.
     * It checks the configured fallback integrator and routes the request accordingly.
     *
     * @param sourceAddress      The source address (short code)
     * @param destinationAddress The destination phone number
     * @param message            The message content
     * @param reqReceiveTime     The time the request was received
     * @param headers            The request headers
     * @return A Mono emitting the WsResponse
     */
    private Mono<WsResponse> tryFallbackIntegration(String sourceAddress, String destinationAddress, String message,
                                                  LocalDateTime reqReceiveTime, Map<String, String> headers) {
        String fallbackIntegrator = properties.getApacheFallbackIntegrator();

        return fallbackService.routeToFallbackIntegrator(fallbackIntegrator, sourceAddress, destinationAddress,
                                                      message, reqReceiveTime, headers)
                .onErrorResume(e -> utilities.handleExceptions(headers, reqReceiveTime, ERR_INTEGRATION_ERROR,
                                                           "Fallback Integration Failed", TRANS_SEND_APACHE_SMS, e, 500));
    }
}
