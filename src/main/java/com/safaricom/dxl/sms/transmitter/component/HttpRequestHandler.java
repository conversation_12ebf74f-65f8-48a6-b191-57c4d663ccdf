package com.safaricom.dxl.sms.transmitter.component;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

@Service
@RequiredArgsConstructor
public class HttpRequestHandler {
    private final WebClient webClient;
    private final MsConfigProperties properties;

    public <T, R> Mono<T> post(String url, Consumer<HttpHeaders> headers, R requestBody, Class<T> responseType) {
        return webClient.post()
                .uri(url)
                .headers(headers)
                .body(BodyInserters.fromValue(requestBody))
                .retrieve()
                .bodyToMono(responseType)
                .retryWhen(Retry.backoff(properties.getHttpRetries(), Duration.ofSeconds(1))
                        .filter(this::shouldRetry));
    }

    /**
     * Determines whether an exception should trigger a retry.
     * Only retries for network-related issues and 5xx server errors.
     *
     * @param throwable The exception that occurred
     * @return true if the request should be retried, false otherwise
     */
    private boolean shouldRetry(Throwable throwable) {
        // Retry for network connectivity issues
        if (throwable instanceof TimeoutException ||
                throwable instanceof IOException) {
            return true;
        }

        // Retry for WebClient response exceptions with 5xx status codes
        if (throwable instanceof WebClientResponseException webClientResponseException) {
            HttpStatus status = HttpStatus.resolve(webClientResponseException.getStatusCode().value());

            // Only retry for 5xx server errors (500, 502, 503, 504, etc.)
            return status != null && status.is5xxServerError();
        }

        // Don't retry for other exceptions (4xx client errors, business logic errors, etc.)
        return false;
    }
}
