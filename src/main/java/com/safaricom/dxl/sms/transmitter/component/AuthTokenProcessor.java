package com.safaricom.dxl.sms.transmitter.component;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.datalayer.dao.ICacheAo;
import com.safaricom.dxl.sms.transmitter.exception.InternalServerErrorException;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.ApacheAuthToken;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.ApacheTokenPayload;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Component
@RequiredArgsConstructor
public class AuthTokenProcessor {
    private static final String SPACE = " ";
    private static final String FETCH_TOKEN = "Fetch Token";
    private final MsConfigProperties properties;
    private final ICacheAo cacheAo;
    private final HttpRequestHandler httpRequestHandler;
    private final WsStarterService wsStarterService;
    LocalDateTime reqReceiveTime = LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE));


    public Mono<String> getAuthToken(Map<String, String> headers) {
        if (properties.isCacheAuthTokenEnabled()) {
            WsLogManager.starterDebug(headers.get(X_CONVERSATION_ID), "Token header", "1s", "Caching enabled, retrieving!");
            return cacheAo.getCache(properties.getApacheAuthTokenKey(), String.class)
                    .flatMap(Mono::just)
                    .switchIfEmpty(fetchNewTokenAndCache(headers))
                    .doOnError(throwable -> handleError(throwable, reqReceiveTime, ERR_REDIS_GET, headers));

        }
        WsLogManager.starterDebug(headers.get(X_CONVERSATION_ID), "Token header", "1s", "Caching disabled, getting fresh token!");
        return fetchNewTokenAndCache(headers);
    }

    protected Mono<String> fetchNewTokenAndCache(Map<String, String> headers) {
        return fetchNewToken()
                .flatMap(token -> cacheToken(token, reqReceiveTime, headers)
                        .thenReturn(token)
                )
                .doOnError(throwable -> handleError(throwable, reqReceiveTime, ERR_REDIS_WRITE, headers));
    }

    private Mono<String> fetchNewToken() {
        return httpRequestHandler.post(properties.getApacheAuthEndpoint(), getAuthTokenHeaders(), apacheAuthTokenPayload(), ApacheAuthToken.class)
                .flatMap(this::extractAuthToken);
    }

    private Mono<Boolean> cacheToken(String token, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return cacheAo.setCache(properties.getApacheAuthTokenKey(), token, Duration.ofMinutes(properties.getAuthTokenExpireTime()))
                .doOnSuccess(cacheSet -> {
                    String responseTime = Duration.between(reqReceiveTime, LocalDateTime.now()).toMillis() + "ms";
                    if (Boolean.TRUE.equals(cacheSet)) {
                        WsLogManager.starterInfo(headers.getOrDefault(X_CONVERSATION_ID, ES), FETCH_TOKEN, responseTime, "Caching Successful");
                    } else {
                        WsLogManager.starterWarn(headers.getOrDefault(X_CONVERSATION_ID, ES), FETCH_TOKEN, responseTime, "Caching FAILED");
                    }
                });
    }

    private Consumer<HttpHeaders> getAuthTokenHeaders() {
        return httpHeaders -> {
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set(HttpHeaders.AUTHORIZATION, properties.getApacheAuthAuthorization());
        };
    }

    private String apacheAuthTokenPayload() {
        ApacheTokenPayload apacheTokenPayload = ApacheTokenPayload.builder()
                .realm(properties.getApacheAuthRealm())
                .clientId(properties.getApacheAuthClientId())
                .username(properties.getApacheAuthUsername())
                .password(properties.getApacheAuthPassword())
                .build();
        return wsStarterService.serialize(apacheTokenPayload);
    }

    private Mono<String> extractAuthToken(Object response) {
        if (response instanceof ApacheAuthToken apacheAuthToken) {
            String token = apacheAuthToken.getTokenType() + SPACE + apacheAuthToken.getAccessToken();
            return Mono.just(token);
        } else {
            return Mono.error(new InternalServerErrorException("Token Retrieve Error: " + response.toString(), ERR_SERVER_ERROR));
        }
    }

    private void handleError(Throwable throwable, LocalDateTime reqReceiveTime, String errorCode, Map<String, String> headers) {
        String responseTime = Duration.between(reqReceiveTime, LocalDateTime.now()).toMillis() + "ms";
        WsLogManager.error(headers.getOrDefault(X_CONVERSATION_ID, ES), ES, FETCH_TOKEN, responseTime, ES, ES, headers.getOrDefault(X_SOURCE_SYSTEM, ES), "Redis", ES, 500, throwable.getMessage(), ES, ES, ES);
        throw new InternalServerErrorException(throwable.getMessage(), errorCode);
    }
}
