package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;

import com.safaricom.dxl.sms.transmitter.datalayer.dao.IMenuForUserAo;
import com.safaricom.dxl.sms.transmitter.datalayer.entities.MenuForUser;
import com.safaricom.dxl.sms.transmitter.datalayer.repositories.MenuForUserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.LOCAL_TIME_ZONE;

/**
 * <AUTHOR>
 */

@Service
@RequiredArgsConstructor
public class MenuForUserAoImpl implements IMenuForUserAo {
    private final MenuForUserRepository menuForUserRepository;

    @Override
    public Mono<MenuForUser> add(MenuForUser menuForUser) {
        return menuForUserRepository.save(menuForUser);
    }

    @Override
    public Mono<MenuForUser> update(MenuForUser menuForUser) {
        menuForUser.setLastUpdated(LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE)));
        return add(menuForUser);
    }

    @Override
    public Mono<MenuForUser> getMenuByUserId(String userId) {
        return menuForUserRepository.findMenuForUserByUserId(userId);
    }

    @Override
    public Mono<MenuForUser> getMenuByUserIdAndShortCode(String userId, String shortCode) {
        return menuForUserRepository.findMenuForUserByUserIdAndShortCode(userId, shortCode);
    }
}
