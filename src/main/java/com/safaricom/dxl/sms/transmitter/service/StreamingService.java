package com.safaricom.dxl.sms.transmitter.service;

import com.safaricom.dxl.encryption.AesCbcEncryptorDecryptor;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.datalayer.dao.IOutboundSMSMetricsAo;
import com.safaricom.dxl.sms.transmitter.datalayer.entities.OutboundSMSMetrics;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.config.WsKafkaProperties;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import com.safaricom.dxl.webflux.starter.service.WsStarterStreamProducer;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.NULL;

@Service
@RequiredArgsConstructor
public class StreamingService {
    private final WsStarterStreamProducer starterStreamProducer;
    private final WsKafkaProperties wsKafkaProperties;
    private final WsStarterService wsStarterService;
    private final MsConfigProperties properties;
    private final Utilities utilities;
    private final IOutboundSMSMetricsAo outboundSMSMetricsAo;
    private final WsResponseMapper responseMapper;

    @Value("${spring.service.name}")
    private String serviceName;

    public Mono<WsResponse> streamSentSms(String sourceAddress, String destAddress, String message, String respCode, String respMessage, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        try {
            long responseTime = Duration.between(reqReceiveTime, LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE))).toMillis();
            OutboundSMSMetrics smsMetrics = OutboundSMSMetrics.builder()
                    .type(OUTBOUND)
                    .serviceName(serviceName)
                    .shortCode(sourceAddress)
                    .phoneNumber(destAddress)
                    .message(message)
                    .created(reqReceiveTime)
                    .apiResCode(respCode)
                    .apiResMessage(respMessage)
                    .latency(String.valueOf(responseTime))
                    .apiResponseTime(LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE)))
                    .build();
            if (properties.isKafkaStreamEnabled()) {
                return sendToKafka(smsMetrics, headers);
            }
            if (properties.isDatabaseStreamEnabled()) {
                return sendToDb(smsMetrics, headers);
            }
            return responseMapper.setApiResponse(ERR_NO_STREAM, NULL, TRANS_STREAM_SERVICE, FALSE, headers);

        } catch (Exception e) {
            return utilities.handleExceptions(headers, reqReceiveTime, ERR_INTEGRATION_ERROR, "Stream Data", TRANS_STREAM_SERVICE, e, 500);
        }

    }

    private Mono<WsResponse> sendToKafka(OutboundSMSMetrics smsMetrics, Map<String, String> headers) {
        if (properties.isZuriStreamEnabled() && utilities.isShortCodeMappedToZuri(smsMetrics.getShortCode())) {
            starterStreamProducer.produce(wsKafkaProperties.getTopic(), wsStarterService.serialize(smsMetrics));
            return responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_KAFKA_STREAM_ZURI, FALSE, headers);
        }
        if (properties.isDubiStreamEnabled() && utilities.isShortCodeMappedToDubi(smsMetrics.getShortCode())) {
            starterStreamProducer.produce(wsKafkaProperties.getTopic(), wsStarterService.serialize(smsMetrics));
            return responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_KAFKA_STREAM_DUBI, FALSE, headers);
        }
        return responseMapper.setApiResponse(ERR_NO_STREAM, NULL, TRANS_KAFKA_STREAM, FALSE, headers);
    }

    private Mono<WsResponse> sendToDb(OutboundSMSMetrics smsMetrics, Map<String, String> headers) {
        String encryptedMsisdn = AesCbcEncryptorDecryptor.encryptInternalMsisdnRequest(properties.getEncryptionKey(), properties.getInitVector(), smsMetrics.getPhoneNumber());
        smsMetrics.setPhoneNumber(encryptedMsisdn);
        if (properties.isDatabaseStreamEnabled()) {
            if (properties.isZuriStreamEnabled() && utilities.isShortCodeMappedToZuri(smsMetrics.getShortCode())) {
                return outboundSMSMetricsAo.addSMSMetrics(smsMetrics)
                        .then(responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_DB_STREAM_ZURI, FALSE, headers));
            }
            if (properties.isDubiStreamEnabled() && utilities.isShortCodeMappedToDubi(smsMetrics.getShortCode())) {
                return outboundSMSMetricsAo.addSMSMetrics(smsMetrics)
                        .then(responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_DB_STREAM_DUBI, FALSE, headers));
            }
        }
        return responseMapper.setApiResponse(ERR_NO_STREAM, NULL, TRANS_DB_STREAM, FALSE, headers);
    }
}
