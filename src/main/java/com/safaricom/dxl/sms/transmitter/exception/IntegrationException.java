package com.safaricom.dxl.sms.transmitter.exception;

import lombok.Generated;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when there is an error with external integrations.
 * This includes errors when communicating with Tibco, Apache, or DXL services.
 */
@ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
public class IntegrationException extends RuntimeException {
    private final String code;
    private final String integration;

    /**
     * Constructs a new IntegrationException with the specified detail message, error code, and integration name.
     *
     * @param message     the detail message
     * @param code        the error code
     * @param integration the name of the integration that failed
     */
    public IntegrationException(String message, String code, String integration) {
        super(message);
        this.code = code;
        this.integration = integration;
    }

    /**
     * Returns the error code associated with this exception.
     *
     * @return the error code
     */
    @Generated
    public String getCode() {
        return this.code;
    }

    /**
     * Returns the name of the integration that failed.
     *
     * @return the integration name
     */
    @Generated
    public String getIntegration() {
        return this.integration;
    }
}
