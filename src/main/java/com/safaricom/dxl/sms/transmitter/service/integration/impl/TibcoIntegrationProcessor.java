package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.TibcoMessage;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Response;
import com.safaricom.dxl.sms.transmitter.service.integration.AbstractIntegrationProcessor;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_INTEGRATION_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.TRANS_SEND_TIBCO_SMS;

/**
 * Integration processor for the Tibco SMS gateway.
 * <p>
 * This processor handles sending SMS messages through the Tibco gateway,
 * which is one of the supported integration channels.
 */
@Service
public class TibcoIntegrationProcessor extends AbstractIntegrationProcessor<TibcoMessage, Response> {
    private final TibcoPayloadConstructor payloadConstructor;
    private final TibcoHeaderConstructor headerConstructor;
    private final MsConfigProperties properties;
    private final TibcoApacheApiRespProcessor apiRespProcessor;
    private final Utilities utilities;

   public TibcoIntegrationProcessor(
            TibcoPayloadConstructor payloadConstructor,
            TibcoHeaderConstructor headerConstructor,
            MsConfigProperties properties,
            HttpRequestHandler httpRequestHandler,
            TibcoApacheApiRespProcessor apiRespProcessor,
            Utilities utilities) {
        super(httpRequestHandler);
        this.payloadConstructor = payloadConstructor;
        this.headerConstructor = headerConstructor;
        this.properties = properties;
        this.apiRespProcessor = apiRespProcessor;
        this.utilities = utilities;
    }

    @Override
    protected TibcoMessage generatePayload(String sourceAddress, String destinationAddress, String message, Map<String, String> headers) {
        return payloadConstructor.generatePayload(sourceAddress, destinationAddress, message, headers);
    }

    @Override
    protected String getEndpoint() {
        return properties.getTibcoEndpoint();
    }

    @Override
    protected Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
        return headerConstructor.constructHeaders(destinationAddress, headers);
    }

    @Override
    protected Class<Response> getResponseClass() {
        return Response.class;
    }

    @Override
    protected Mono<WsResponse> processResponse(Response response, String sourceAddress, String destinationAddress, String endpoint, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endpoint, message, reqReceiveTime, headers);
    }

    @Override
    protected Mono<WsResponse> handleError(Throwable error, String sourceAddress, String destinationAddress, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return utilities.handleExceptions(headers, reqReceiveTime, ERR_INTEGRATION_ERROR, "Tibco API Call", TRANS_SEND_TIBCO_SMS, error, 500);
    }
}
