package com.safaricom.dxl.sms.transmitter.datalayer.repositories;


import com.safaricom.dxl.sms.transmitter.datalayer.entities.Apps;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */

@Repository
public interface AppsRepository extends ReactiveMongoRepository<Apps, String> {

    @Query("{'appName': ?0, 'appSecretId': ?1}")
    Mono<Apps> findByAppNameAndAppSecretId(String appName, String appSecretId);

    @Query("{'authToken': ?0}")
    Mono<Apps> findByAuthToken(String authToken);
}
