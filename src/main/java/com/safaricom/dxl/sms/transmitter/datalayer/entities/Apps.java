package com.safaricom.dxl.sms.transmitter.datalayer.entities;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@ToString
public class Apps implements Serializable {
    @Id
    private String appId;
    private String appName;
    private String tagName;
    private String appSecretId;
    private String authToken;
    private String webhook;
    private String accessToken;
    private String domain;
    private String persona;
    private Date created;
}
