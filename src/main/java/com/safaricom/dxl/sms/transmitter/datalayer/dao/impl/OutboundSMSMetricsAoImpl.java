package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;


import com.safaricom.dxl.sms.transmitter.datalayer.dao.IOutboundSMSMetricsAo;
import com.safaricom.dxl.sms.transmitter.datalayer.entities.OutboundSMSMetrics;
import com.safaricom.dxl.sms.transmitter.datalayer.repositories.OutboundSMSMetricsRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.LOCAL_TIME_ZONE;

@Service
@RequiredArgsConstructor
public class OutboundSMSMetricsAoImpl implements IOutboundSMSMetricsAo {
    private final OutboundSMSMetricsRepository outboundSMSMetricsRepository;


    @Override
    public Mono<OutboundSMSMetrics> addSMSMetrics(OutboundSMSMetrics smsMetrics) {
        smsMetrics.setCreated(LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE)));
        return outboundSMSMetricsRepository.save(smsMetrics);
    }
}
