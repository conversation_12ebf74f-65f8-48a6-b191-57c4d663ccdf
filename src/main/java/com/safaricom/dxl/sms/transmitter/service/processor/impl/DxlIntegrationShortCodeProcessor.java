package com.safaricom.dxl.sms.transmitter.service.processor.impl;

import com.safaricom.dxl.sms.transmitter.service.integration.impl.DxlIntegrationProcessor;
import com.safaricom.dxl.sms.transmitter.service.processor.IApiProcessor;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class DxlIntegrationShortCodeProcessor implements IApiProcessor {
    private final DxlIntegrationProcessor dxlIntegrationProcessor;
    private final Utilities utilities;

    @Override
    public boolean supports(String shortCode) {
        return utilities.isShortCodeMappedToDxl(shortCode);
    }

    @Override
    public Mono<WsResponse> process(String sourceAddress, String destinationAddress, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        return dxlIntegrationProcessor.processIntegrationRequest(sourceAddress, destinationAddress, message, reqReceiveTime, headers);
    }
}
