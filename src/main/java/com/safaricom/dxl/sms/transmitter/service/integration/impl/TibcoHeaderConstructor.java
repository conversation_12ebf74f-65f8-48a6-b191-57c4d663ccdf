package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.service.integration.IHeaderConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.SERVICE_NAME;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_SOURCE_SYSTEM;

@Component
@RequiredArgsConstructor
public class TibcoHeaderConstructor implements IHeaderConstructor {
    @Override
    public Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
        return Mono.just(httpHeaders -> {
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set(HttpHeaders.ACCEPT_ENCODING, MediaType.APPLICATION_JSON_VALUE);
            httpHeaders.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
            httpHeaders.set(HttpHeaders.AUTHORIZATION, "Basic ZWFpX3Ntc19zZW5kZXI6ZWExQFNNU3NlbmRlciE=");
            httpHeaders.set(X_SOURCE_SYSTEM, SERVICE_NAME);
            httpHeaders.set(X_CONVERSATION_ID, headers.get(X_CONVERSATION_ID));
        });
    }
}
