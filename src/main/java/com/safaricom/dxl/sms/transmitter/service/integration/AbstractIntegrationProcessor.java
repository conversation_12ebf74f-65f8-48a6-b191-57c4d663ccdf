package com.safaricom.dxl.sms.transmitter.service.integration;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Abstract base class for integration processors that provides common functionality.
 * <p>
 * This class implements the template method pattern to standardize the process flow
 * for all integration processors while allowing specific steps to be customized by
 * subclasses.
 *
 * @param <P> The payload type for the integration
 * @param <R> The response type from the external system
 */
@RequiredArgsConstructor
public abstract class AbstractIntegrationProcessor<P, R> implements IIntegrationProcessor {

    protected final HttpRequestHandler httpRequestHandler;

    /**
     * Template method that defines the standard process flow for all integration processors.
     * <p>
     * The flow consists of:
     * 1. Generating the payload
     * 2. Getting the endpoint
     * 3. Constructing headers
     * 4. Making the HTTP request
     * 5. Processing the response
     * 6. Handling errors
     *
     * @param sourceAddress      The source address (short code)
     * @param destinationAddress The destination phone number
     * @param message            The message content
     * @param reqReceiveTime     The time the request was received
     * @param headers            The request headers
     * @return A Mono emitting the WsResponse
     */
    @Override
    public Mono<WsResponse> processIntegrationRequest(String sourceAddress, String destinationAddress, String message, 
                                                     LocalDateTime reqReceiveTime, Map<String, String> headers) {
        P payload = generatePayload(sourceAddress, destinationAddress, message, headers);
        String endpoint = getEndpoint();
        
        return constructHeaders(destinationAddress, headers)
                .flatMap(httpHeaders -> httpRequestHandler.post(endpoint, httpHeaders, payload, getResponseClass()))
                .flatMap(response -> processResponse(response, sourceAddress, destinationAddress, endpoint, message, reqReceiveTime, headers))
                .onErrorResume(error -> handleError(error, sourceAddress, destinationAddress, message, reqReceiveTime, headers));
    }

    /**
     * Generates the payload for the integration request.
     *
     * @param sourceAddress      The source address (short code)
     * @param destinationAddress The destination phone number
     * @param message            The message content
     * @param headers            The request headers
     * @return The payload object
     */
    protected abstract P generatePayload(String sourceAddress, String destinationAddress, String message, Map<String, String> headers);

    /**
     * Gets the endpoint URL for the integration.
     *
     * @return The endpoint URL
     */
    protected abstract String getEndpoint();

    /**
     * Constructs the headers for the integration request.
     *
     * @param destinationAddress The destination phone number
     * @param headers            The request headers
     * @return A Mono emitting the headers consumer
     */
    protected abstract Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers);

    /**
     * Gets the response class for deserialization.
     *
     * @return The response class
     */
    protected abstract Class<R> getResponseClass();

    /**
     * Processes the response from the integration.
     *
     * @param response           The response from the integration
     * @param sourceAddress      The source address (short code)
     * @param destinationAddress The destination phone number
     * @param endpoint           The endpoint URL
     * @param message            The message content
     * @param reqReceiveTime     The time the request was received
     * @param headers            The request headers
     * @return A Mono emitting the WsResponse
     */
    protected abstract Mono<WsResponse> processResponse(R response, String sourceAddress, String destinationAddress, 
                                                      String endpoint, String message, LocalDateTime reqReceiveTime, 
                                                      Map<String, String> headers);

    /**
     * Handles errors that occur during the integration process.
     *
     * @param error              The error that occurred
     * @param sourceAddress      The source address (short code)
     * @param destinationAddress The destination phone number
     * @param message            The message content
     * @param reqReceiveTime     The time the request was received
     * @param headers            The request headers
     * @return A Mono emitting the WsResponse
     */
    protected abstract Mono<WsResponse> handleError(Throwable error, String sourceAddress, String destinationAddress, 
                                                  String message, LocalDateTime reqReceiveTime, Map<String, String> headers);
}
