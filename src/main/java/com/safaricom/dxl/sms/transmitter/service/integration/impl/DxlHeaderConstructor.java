package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.service.integration.IHeaderConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.function.Consumer;


@Component
@RequiredArgsConstructor
public class DxlHeaderConstructor implements IHeaderConstructor {

    @Override
    public Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
        return Mono.just(httpHeaders -> headers.forEach(httpHeaders::set));
    }
}
