package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.service.integration.IHeaderConstructor;
import com.safaricom.dxl.webflux.starter.enums.WsProcessLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_MSISDN;

@Component
@RequiredArgsConstructor
public class DxlHeaderConstructor implements IHeaderConstructor {

    @Override
    public Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
        return Mono.just(httpHeaders -> {
            headers.forEach(httpHeaders::set);
            // Only modify the HttpHeaders object, not the original headers Map
            httpHeaders.set(X_<PERSON>ISDN, destinationAddress);
            WsProcessLogger.INFO.log("HEADERS IN CONSTRUCTOR: " + headers);
        });
    }
}
