package com.safaricom.dxl.sms.transmitter.model.dto;

import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Represents an SMS request from the API.
 * <p>
 * Contains all the necessary information to send an SMS, including the short code,
 * customer phone number, and message content.
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class SmsRequest {
    @NotBlank(message = "Correlation ID is required")
    @Pattern(regexp = "^[A-Za-z0-9-]+$", message = "Correlation ID can only contain letters, numbers, and hyphens")
    @Size(max = 50, message = "Correlation ID cannot exceed 50 characters")
    private String correlationId;

    @NotBlank(message = "Short code is required")
    @Size(min = 3, max = 5, message = "Short code must be between 3 and 5 digits")
    @Pattern(regexp = "^\\d+$", message = "Short code must contain only digits")
    private String shortCode;

    @NotBlank(message = "Customer phone number is required")
    @Pattern(regexp = "^(254|0)?[71]\\d{8}$", message = "Customer phone number format is invalid")
    private String customerPhoneNumber;

    @NotNull(message = "Message is required")
    @Valid
    private Message message;
}