package com.safaricom.dxl.sms.transmitter.model.pojo.tibco;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApacheTokenPayload {
    @JsonProperty("realm")
    private String realm;
    @JsonProperty("client_id")
    private String clientId;
    @JsonProperty("username")
    private String username;
    @JsonProperty("password")
    private String password;
}
