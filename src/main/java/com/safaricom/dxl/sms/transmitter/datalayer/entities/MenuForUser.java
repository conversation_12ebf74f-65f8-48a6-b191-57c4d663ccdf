package com.safaricom.dxl.sms.transmitter.datalayer.entities;

import com.safaricom.dxl.sms.transmitter.model.pojo.Menu;
import lombok.*;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class MenuForUser {
    @Id
    private String id;
    private String userId;
    private String shortCode;
    private boolean available;
    private List<Menu> menu;
    private LocalDateTime created;
    private LocalDateTime lastUpdated;
}
