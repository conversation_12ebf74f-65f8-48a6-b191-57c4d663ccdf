package com.safaricom.dxl.sms.transmitter.entry.controller;

import com.safaricom.dxl.sms.transmitter.model.dto.SmsRequest;
import com.safaricom.dxl.sms.transmitter.service.SmsRequestRouter;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.LOCAL_TIME_ZONE;

/**
 * REST controller for handling SMS transmission requests (API v1).
 * <p>
 * This controller provides endpoints for sending SMS messages through the v1 API.
 * It uses token-based authentication where the auth token is provided as a path variable.
 * <p>
 * The controller delegates the actual processing to the RequestRouter service,
 * which handles routing to the appropriate integration channel based on the short code.
 *
 * <AUTHOR>
 */
@Tag(name = "Ms SMS Transmitter", description = "SMS transmission endpoints")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/integrations")
public class SendSmsControllerV1 {
    private final SmsRequestRouter smsRequestRouter;

    /**
     * Sends an SMS message using the provided authentication token and SMS request details.
     * <p>
     * This endpoint accepts an SMS request containing the recipient's phone number,
     * the message content, and the short code. The auth token is used to authenticate
     * the request and must match the configured token for the specified short code.
     * <p>
     * The endpoint returns immediately with a 202 Accepted response, and the actual
     * SMS processing happens asynchronously.
     *
     * @param headers HTTP request headers containing conversation IDs and other metadata
     * @param authToken Authentication token for the SMS service
     * @param smsRequest Request object containing recipient phone number, message content, and short code
     * @return A Mono emitting a WsResponse with the status of the request acceptance
     */
    @Operation(summary = "Send SMS", description = "Sends an SMS message to the specified recipient")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "202", description = "Request accepted for processing"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
        @ApiResponse(responseCode = "401", description = "Authentication failed"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/{authToken}/sendsms")
    public Mono<WsResponse> sendSms(
            @RequestHeader Map<String, String> headers,
            @Parameter(description = "Authentication token for the SMS service") @PathVariable String authToken,
            @Parameter(description = "SMS request details", required = true, content = @Content(schema = @Schema(implementation = SmsRequest.class)))
            @Valid @RequestBody SmsRequest smsRequest) {

        // Record the time the request was received for metrics and logging
        LocalDateTime receiveTime = LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE));

        // Delegate to the request router for processing
        return smsRequestRouter.handleApiRequest(smsRequest, authToken, receiveTime, headers);
    }
}