package com.safaricom.dxl.sms.transmitter.model.pojo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Represents a recipient of an SMS message in the ChatBot flow.
 * <p>
 * Contains information about the recipient including the short code (appSecretId)
 * and the phone number (userId).
 */
@NoArgsConstructor
@Setter
@Getter
@ToString
public class Recipient {
    private String trackUser;
    private String appName;

    @NotBlank(message = "Short code (appSecretId) is required")
    @Size(min = 3, max = 5, message = "Short code must be between 3 and 5 digits")
    @Pattern(regexp = "^\\d+$", message = "Short code must contain only digits")
    private String appSecretId;

    @NotBlank(message = "Phone number (userId) is required")
    @Pattern(regexp = "^(254|0)?[71]\\d{8}$", message = "Phone number format is invalid")
    private String userId;

    private String appId;
    private String webhook;
    private String title;
    private String authToken;
}
