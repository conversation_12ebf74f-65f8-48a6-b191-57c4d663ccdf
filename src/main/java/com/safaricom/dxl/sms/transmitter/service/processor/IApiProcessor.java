package com.safaricom.dxl.sms.transmitter.service.processor;

import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Interface defining the contract for API processors that handle SMS routing based on short codes.
 * <p>
 * API processors are responsible for determining which integration channel should handle a specific
 * SMS request based on the short code, and then delegating the actual processing to the appropriate
 * integration processor.
 * <p>
 * This interface follows the Chain of Responsibility pattern, where each implementation checks if it
 * supports the given short code, and if so, processes the request. Otherwise, the request is passed
 * to the next processor in the chain.
 */
public interface IApiProcessor {

    /**
     * Determines whether this processor supports the given short code.
     * <p>
     * Each implementation checks if the short code is in its list of supported short codes.
     * This method is used to determine which processor should handle a specific SMS request.
     *
     * @param shortCode The short code to check
     * @return true if this processor supports the short code, false otherwise
     */
    boolean supports(String shortCode);

    /**
     * Processes an SMS request for a supported short code.
     * <p>
     * This method is called after the supports() method returns true. It delegates the actual
     * processing to the appropriate integration processor based on the short code.
     *
     * @param sourceAddress The source address (short code) of the SMS
     * @param destinationAddress The destination phone number
     * @param message The message content to be sent
     * @param reqReceiveTime The time when the original request was received
     * @param headers HTTP headers from the original request
     * @return A Mono emitting a WsResponse with the result of the processing
     */
    Mono<WsResponse> process(String sourceAddress, String destinationAddress, String message, LocalDateTime reqReceiveTime, Map<String, String> headers);
}
