package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.CharacteristicValue;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Response;
import com.safaricom.dxl.sms.transmitter.service.StreamingService;
import com.safaricom.dxl.sms.transmitter.service.integration.IApiRespProcessor;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Service
@RequiredArgsConstructor
public class TibcoApacheApiRespProcessor implements IApiRespProcessor<Response> {
    private final StreamingService streamingService;
    private final WsResponseMapper responseMapper;
    private final MsConfigProperties properties;

    @Override
    public Mono<WsResponse> processApiResponse(Response response, String sourceAddress, String destinationAddress, String endPoint, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        long responseTime = Duration.between(reqReceiveTime, LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE))).toMillis();

        CharacteristicValue responseCode = response.getParts().getSpecification().getCharacteristicValue().stream()
                .filter(charValue -> charValue.getCharacteristicName().equalsIgnoreCase("ResponseCode"))
                .findFirst()
                .orElse(new CharacteristicValue("ResponseCode", "999"));

        CharacteristicValue responseDescValue = response.getParts().getSpecification().getCharacteristicValue().stream()
                .filter(charValue -> charValue.getCharacteristicName().equalsIgnoreCase("ResponseDescription"))
                .findFirst()
                .orElse(new CharacteristicValue("ResponseDescription", "Error from SMS Integration"));

        String responseMessage = responseDescValue.getValue();
        String process = endPoint.contains(properties.getTibcoUrlKeyword()) ? "TIBCO" : "APACHE";

        WsLogManager.starterInfo(headers.get(X_CONVERSATION_ID), process + "-SMS-Integration", String.valueOf(responseTime), endPoint);

        String processParams = "API_CALL,SEND_SMS" + sourceAddress + "," + process + "_TX_INTEGRATION";
        if (responseCode.getValue().equalsIgnoreCase("00") || responseCode.getValue().equalsIgnoreCase("1000")) {
            return streamingService.streamSentSms(sourceAddress, destinationAddress, message, responseCode.getValue(), responseMessage, reqReceiveTime, headers)
                    .then(responseMapper.setApiResponse(ERR_SUCCESS, NULL, processParams, responseMessage, FALSE, headers));

        } else {
            return streamingService.streamSentSms(sourceAddress, destinationAddress, message, responseCode.getValue(), responseMessage, reqReceiveTime, headers)
                    .then(responseMapper.setApiResponse(ERR_INTEGRATION_ERROR, NULL, processParams, responseMessage, FALSE, headers));
        }
    }
}
