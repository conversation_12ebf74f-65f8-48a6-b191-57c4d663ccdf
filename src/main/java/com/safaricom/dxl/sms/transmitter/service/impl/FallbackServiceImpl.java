package com.safaricom.dxl.sms.transmitter.service.impl;

import com.safaricom.dxl.sms.transmitter.service.FallbackService;
import com.safaricom.dxl.sms.transmitter.service.integration.impl.DxlIntegrationProcessor;
import com.safaricom.dxl.sms.transmitter.service.integration.impl.TibcoIntegrationProcessor;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;

/**
 * Implementation of the FallbackService interface.
 * <p>
 * This service handles routing SMS requests to alternative integration channels
 * when the primary integration fails.
 */
@Service
@RequiredArgsConstructor
public class FallbackServiceImpl implements FallbackService {

    // Constants
    private static final String PROCESS_FALLBACK_ROUTING = "Fallback Integration";

    private final TibcoIntegrationProcessor tibcoIntegrationProcessor;
    private final DxlIntegrationProcessor dxlIntegrationProcessor;
    private final WsResponseMapper responseMapper;

    @Override
    public Mono<WsResponse> routeToFallbackIntegrator(String fallbackType, String sourceAddress,
                                                     String destinationAddress, String message,
                                                     LocalDateTime requestTime, Map<String, String> headers) {
        // Log the fallback attempt
        WsLogManager.starterInfo(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                               "Attempting fallback to " + fallbackType + " integration");

        // Check if fallback is disabled
        if ("NONE".equalsIgnoreCase(fallbackType)) {
            WsLogManager.starterWarn(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                                  "No fallback configured. Giving up.");
            return Mono.error(new Exception("Max retries exceeded and no fallback configured"));
        }

        // Route to the appropriate fallback integrator
        if ("TIBCO".equalsIgnoreCase(fallbackType)) {
            return routeToTibcoIntegration(sourceAddress, destinationAddress, message, requestTime, headers);
        } else if ("DXL".equalsIgnoreCase(fallbackType)) {
            return routeToDxlIntegration(sourceAddress, destinationAddress, message, requestTime, headers);
        }

        // If fallback integrator is not recognized, handle as error
        return Mono.error(new Exception("Unrecognized fallback integrator: " + fallbackType));
    }

    @Override
    public Mono<WsResponse> routeToTibcoIntegration(String sourceAddress, String destinationAddress, String message,
                                                  LocalDateTime reqReceiveTime, Map<String, String> headers) {
        try {
            // Log the fallback routing
            WsLogManager.starterInfo(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                                   "Routing to TIBCO integration");

            // Check if the Tibco integration processor is available
            if (tibcoIntegrationProcessor == null) {
                WsLogManager.starterWarn(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                                      "TIBCO integration processor not available");
                return Mono.error(new Exception("TIBCO integration processor not available"));
            }

            // Use the injected Tibco integration processor
            return tibcoIntegrationProcessor.processIntegrationRequest(
                sourceAddress, destinationAddress, message, reqReceiveTime, headers);
        } catch (Exception e) {
            // Log the error
            WsLogManager.starterError(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                                    "Error routing to TIBCO integration: " + e.getMessage());

            // Return an error response
            return Mono.error(new Exception("TIBCO Fallback Failed: " + e.getMessage()));
        }
    }

    @Override
    public Mono<WsResponse> routeToDxlIntegration(String sourceAddress, String destinationAddress, String message,
                                                LocalDateTime reqReceiveTime, Map<String, String> headers) {
        try {
            // Log the fallback routing
            WsLogManager.starterInfo(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                                   "Routing to DXL integration");

            // Check if the DXL integration processor is available
            if (dxlIntegrationProcessor == null) {
                WsLogManager.starterWarn(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                                      "DXL integration processor not available");
                return Mono.error(new Exception("DXL integration processor not available"));
            }

            // Use the injected DXL integration processor
            return dxlIntegrationProcessor.processIntegrationRequest(
                sourceAddress, destinationAddress, message, reqReceiveTime, headers);
        } catch (Exception e) {
            // Log the error
            WsLogManager.starterError(headers.get(X_CONVERSATION_ID), PROCESS_FALLBACK_ROUTING, "0ms",
                                    "Error routing to DXL integration: " + e.getMessage());

            // Return an error response
            return Mono.error(new Exception("DXL Fallback Failed: " + e.getMessage()));
        }
    }
}
