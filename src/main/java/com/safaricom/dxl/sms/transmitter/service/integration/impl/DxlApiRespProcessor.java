package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.service.StreamingService;
import com.safaricom.dxl.sms.transmitter.service.integration.IApiRespProcessor;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Service
@RequiredArgsConstructor
public class DxlApiRespProcessor implements IApiRespProcessor<WsResponse> {
    private final MsConfigProperties properties;
    private final StreamingService streamingService;
    private final WsResponseMapper responseMapper;

    @Override
    public Mono<WsResponse> processApiResponse(WsResponse response, String sourceAddress, String destinationAddress, String endPoint, String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        long responseTime = Duration.between(reqReceiveTime, LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE))).toMillis();
        String responseMessage = response.getHeader().getResponseMessage();
        int responseCode = response.getHeader().getResponseCode();

        WsLogManager.starterInfo(headers.get(X_CONVERSATION_ID), "DXL-SMS-Integration", String.valueOf(responseTime), properties.getDxlEndpoint());

        streamingService.streamSentSms(sourceAddress, destinationAddress, message, String.valueOf(responseCode), responseMessage, reqReceiveTime, headers);

        String processParams = "API_CALL,SEND_SMS" + sourceAddress + ",DXL_TX_INTEGRATION";
        if (responseCode == 200) {
            return responseMapper.setApiResponse(ERR_SUCCESS, NULL, processParams, responseMessage, FALSE, headers);
        } else {
            return responseMapper.setApiResponse(ERR_INTEGRATION_ERROR, NULL, processParams, responseMessage, FALSE, headers);
        }
    }
}
