package com.safaricom.dxl.sms.transmitter;

import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.mongodb.repository.config.EnableReactiveMongoRepositories;

/**
 * Main application class for the SMS Transmitter microservice.
 * <p>
 * This microservice is responsible for handling SMS transmission requests, routing them to the appropriate
 * integration channel (Tibco, Apache, DXL), and delivering the SMS to the intended recipient.
 * <p>
 * The service uses Spring WebFlux for reactive programming, MongoDB for data storage,
 * Redis for caching, and RabbitMQ for message queuing.
 * <p>
 * Key features include:
 * <ul>
 *   <li>Routing SMS requests to appropriate integration channels</li>
 *   <li>Message format conversion for different channels</li>
 *   <li>Authentication token management</li>
 *   <li>Metrics collection and streaming</li>
 *   <li>Error handling and logging</li>
 * </ul>
 *
 * <AUTHOR>
 */
@EnableCaching // Enables Spring's caching support for Redis
@EnableReactiveMongoRepositories // Enables reactive MongoDB repositories
@EnableRabbit // Enables RabbitMQ message handling
@SpringBootApplication
public class MsSmsTransmitterApplication {
    /**
     * Application entry point.
     * <p>
     * Initializes and starts the Spring Boot application with specific configuration.
     * Command line properties are disabled to ensure all configuration comes from
     * property files or environment variables.
     *
     * @param args Command line arguments (not used)
     */
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(MsSmsTransmitterApplication.class);
        springApplication.setAddCommandLineProperties(false); // Disable command line properties
        springApplication.run();
    }
}