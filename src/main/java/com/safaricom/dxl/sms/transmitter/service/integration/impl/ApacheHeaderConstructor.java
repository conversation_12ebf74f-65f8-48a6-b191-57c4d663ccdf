package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.AuthTokenProcessor;
import com.safaricom.dxl.sms.transmitter.service.integration.IHeaderConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.function.Consumer;

@Component
@RequiredArgsConstructor
public class ApacheHeaderConstructor implements IHeaderConstructor {
    private final AuthTokenProcessor authTokenProcessor;

    @Override
    public Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
        return authTokenProcessor.getAuthToken(headers)
                .map(token -> httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    httpHeaders.set(HttpHeaders.AUTHORIZATION, token);
                });
    }
}