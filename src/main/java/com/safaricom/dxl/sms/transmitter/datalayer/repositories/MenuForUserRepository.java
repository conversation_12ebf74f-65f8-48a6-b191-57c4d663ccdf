package com.safaricom.dxl.sms.transmitter.datalayer.repositories;

import com.safaricom.dxl.sms.transmitter.datalayer.entities.MenuForUser;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */

@Repository
public interface MenuForUserRepository extends ReactiveMongoRepository<MenuForUser, String> {

    @Query("{'userId':?0}")
    Mono<MenuForUser> findMenuForUserByUserId(String userId);

    @Query("{'userId': ?0, 'shortCode': ?1}")
    Mono<MenuForUser> findMenuForUserByUserIdAndShortCode(String userId, String shortCode);
}
