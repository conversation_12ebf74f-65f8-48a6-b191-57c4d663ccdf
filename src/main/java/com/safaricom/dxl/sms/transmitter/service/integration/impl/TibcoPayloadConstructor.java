package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.*;
import com.safaricom.dxl.sms.transmitter.service.integration.IPayloadConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class TibcoPayloadConstructor implements IPayloadConstructor<TibcoMessage> {
    @Override
    public TibcoMessage generatePayload(String shortCode, String destinationAddress, String message, Map<String, String> headers) {
        List<Id> id = new ArrayList<>();
        id.add(Id.builder().value(destinationAddress).build());
        Receiver receiver = Receiver.builder().id(id).build();
        Roles roles = Roles.builder().receiver(receiver).build();
        Trailer trailer = Trailer.builder().text(shortCode).build();
        Body body = Body.builder().text(message).build();
        com.safaricom.dxl.sms.transmitter.model.pojo.tibco.Parts parts =
                com.safaricom.dxl.sms.transmitter.model.pojo.tibco.Parts.builder()
                        .body(body).trailer(trailer).build();
        return TibcoMessage.builder().roles(roles).parts(parts).build();
    }
}
