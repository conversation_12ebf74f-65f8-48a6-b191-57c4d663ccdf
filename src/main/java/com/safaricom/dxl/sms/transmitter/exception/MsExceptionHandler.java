package com.safaricom.dxl.sms.transmitter.exception;

import com.safaricom.dxl.webflux.starter.exception.WsExceptionHandler;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsMappingService;
import com.safaricom.dxl.webflux.starter.utils.WsStarterVariables;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.util.Objects;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.ES;

/**
 * Global exception handler for the SMS Transmitter microservice.
 * Handles all custom exceptions and provides appropriate HTTP responses.
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class MsEx<PERSON><PERSON><PERSON><PERSON> extends WsExceptionHandler {

    public MsExceptionHandler(WsMappingService mappingService) {
        super(mappingService);
    }

    @ExceptionHandler({InternalServerErrorException.class})
    protected Mono<ResponseEntity<WsResponse>> internalServerErrorException(InternalServerErrorException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler({BadRequestException.class})
    protected Mono<ResponseEntity<WsResponse>> badRequestException(BadRequestException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({IntegrationException.class})
    protected Mono<ResponseEntity<WsResponse>> integrationException(IntegrationException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.SERVICE_UNAVAILABLE);
    }

    @ExceptionHandler({WsUnauthorizedException.class})
    protected Mono<ResponseEntity<WsResponse>> unauthorizedException(WsUnauthorizedException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler({ResponseStatusException.class})
    protected Mono<ResponseEntity<WsResponse>> responseStatusException(ResponseStatusException ex, ServerHttpRequest request) {
        return setErrResponse(String.valueOf(ex.getStatusCode().value()), ex.getReason(), request, HttpStatus.valueOf(ex.getStatusCode().value()));
    }

    protected Mono<ResponseEntity<WsResponse>> setErrResponse(String code, String message, ServerHttpRequest request, HttpStatus status) {
        return this.setErrResponse(code, Objects.requireNonNullElse(message, ES), WsStarterVariables.NULL, request.getHeaders(), status, true);
    }
}