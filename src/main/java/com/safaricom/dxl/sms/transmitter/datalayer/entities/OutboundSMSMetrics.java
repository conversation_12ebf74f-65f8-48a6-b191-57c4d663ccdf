package com.safaricom.dxl.sms.transmitter.datalayer.entities;

import lombok.*;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OutboundSMSMetrics {
    @Id
    private String id;
    private String type;
    private String shortCode;
    private String phoneNumber;
    private String message;
    private String apiResCode;
    private String apiResMessage;
    private String latency;
    private String serviceName;
    private LocalDateTime apiResponseTime;
    private LocalDateTime created;
}
