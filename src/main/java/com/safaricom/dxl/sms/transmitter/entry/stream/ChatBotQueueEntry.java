package com.safaricom.dxl.sms.transmitter.entry.stream;

import com.safaricom.dxl.sms.transmitter.model.dto.ChatBotRequest;
import com.safaricom.dxl.sms.transmitter.service.SmsRequestRouter;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.LOCAL_TIME_ZONE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;

@Component
@RequiredArgsConstructor
public class ChatBotQueueEntry {
    private final SmsRequestRouter smsRequestRouter;
    private final Utilities utilities;

    @RabbitListener(queues = "${dxl.ms.sms-request-queue}")
    public Mono<Void> receiveMessageFromChatBot(@Valid ChatBotRequest chatBotRequest) {
        LocalDateTime requestReceivedTime = LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE));
        String msisdn = chatBotRequest.getRecipient().getUserId();
        Map<String, String> headers = utilities.createHeadersWithDefaults(chatBotRequest.getCorrelationId(), msisdn, "zuri");

        return smsRequestRouter.handleChatBotRequest(chatBotRequest, requestReceivedTime, headers)
                .onErrorResume(error -> {
                    WsLogManager.starterError(
                            headers.getOrDefault(X_CONVERSATION_ID, ""),
                            "ChatBotQueueEntry",
                            "0ms",
                            "Error processing message: " + error.getMessage()
                    );
                    return Mono.empty();
                })
                .then();
    }
}
