package com.safaricom.dxl.sms.transmitter.entry.stream;

import com.safaricom.dxl.sms.transmitter.model.dto.ChatBotRequest;
import com.safaricom.dxl.sms.transmitter.service.SmsRequestRouter;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import com.rabbitmq.client.Channel;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.LOCAL_TIME_ZONE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;

@Component
@RequiredArgsConstructor
public class ChatBotQueueEntry {
    private final SmsRequestRouter smsRequestRouter;
    private final Utilities utilities;

    @RabbitListener(
            queues = "${dxl.ms.sms-request-queue}",
            ackMode = "MANUAL"
    )
    public Mono<Void> receiveMessageFromChatBot(
            @Valid ChatBotRequest chatBotRequest,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        LocalDateTime requestReceivedTime = LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE));
        String msisdn = chatBotRequest.getRecipient().getUserId();
        Map<String, String> headers = utilities.createHeadersWithDefaults(chatBotRequest.getCorrelationId(), msisdn, "zuri");

        return smsRequestRouter.handleChatBotRequest(chatBotRequest, requestReceivedTime, headers)
                .doOnSuccess(result -> {
                    try {
                        // Acknowledge the message on successful processing
                        channel.basicAck(deliveryTag, false);
                    } catch (Exception e) {
                        WsLogManager.starterError(
                                headers.getOrDefault(X_CONVERSATION_ID, ""),
                                "ChatBotQueueEntry",
                                "0ms",
                                "Error acknowledging message: " + e.getMessage()
                        );
                    }
                })
                .onErrorResume(error -> {
                    try {
                        // Reject the message on error (requeue = false to avoid infinite loops)
                        channel.basicNack(deliveryTag, false, false);
                    } catch (Exception e) {
                        WsLogManager.starterError(
                                headers.getOrDefault(X_CONVERSATION_ID, ""),
                                "ChatBotQueueEntry",
                                "0ms",
                                "Error rejecting message: " + e.getMessage()
                        );
                    }

                    WsLogManager.starterError(
                            headers.getOrDefault(X_CONVERSATION_ID, ""),
                            "ChatBotQueueEntry",
                            "0ms",
                            "Error processing message: " + error.getMessage()
                    );
                    return Mono.empty();
                })
                .then();
    }
}
