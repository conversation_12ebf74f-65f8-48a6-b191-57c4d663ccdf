package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;

import com.safaricom.dxl.sms.transmitter.datalayer.dao.ICacheAo;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Objects;

import static com.safaricom.dxl.sms.transmitter.utils.Utilities.deserializeFromJson;
import static com.safaricom.dxl.sms.transmitter.utils.Utilities.serializeToJson;


@Component
@RedisHash
public class CacheAoImpl implements ICacheAo {


    private final ReactiveRedisTemplate<String, Object> redisTemplate;


    public CacheAoImpl(ReactiveRedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    @Override
    public <T> Mono<Boolean> setCache(String key, T value, Duration duration) {
        ReactiveValueOperations<String, Object> var10000 = this.redisTemplate.opsForValue();
        return var10000.set(key, Objects.requireNonNull(serializeToJson(value)), duration);
    }

    @Override
    public <T> Mono<T> getCache(String key, Class<T> clazz) {
        ReactiveValueOperations<String, Object> var10000 = this.redisTemplate.opsForValue();
        return var10000.get(key).mapNotNull(json -> deserializeFromJson(json.toString(), clazz));
    }

    @Override
    public Mono<Boolean> deleteCache(String key) {
        ReactiveValueOperations<String, Object> var10000 = this.redisTemplate.opsForValue();
        return var10000.delete(key);
    }
}
