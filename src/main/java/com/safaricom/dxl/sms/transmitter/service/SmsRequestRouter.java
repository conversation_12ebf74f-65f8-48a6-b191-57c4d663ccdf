package com.safaricom.dxl.sms.transmitter.service;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.datalayer.dao.IAppsAo;
import com.safaricom.dxl.sms.transmitter.exception.BadRequestException;
import com.safaricom.dxl.sms.transmitter.exception.WsUnauthorizedException;
import com.safaricom.dxl.sms.transmitter.model.dto.ChatBotRequest;
import com.safaricom.dxl.sms.transmitter.model.dto.SmsRequest;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

/**
 * Service responsible for routing SMS requests to the appropriate processors.
 * <p>
 * The RequestRouter is the entry point for all SMS transmission requests, whether they come
 * from REST API endpoints or message queues. It handles authentication, validation, and routing
 * of requests to the appropriate processing components based on the short code and other parameters.
 * <p>
 * This service implements an asynchronous processing model where requests are immediately acknowledged
 * with a 202 Accepted response, and the actual processing happens in the background on separate threads.
 * <p>
 * The router supports two main types of requests:
 * <ul>
 *   <li>API requests - Coming from REST endpoints with authentication tokens</li>
 *   <li>ChatBot requests - Coming from message queues with recipient and message details</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SmsRequestRouter {

    private final SmsMessageProcessor smsMessageProcessor;
    private final WsResponseMapper responseMapper;
    private final IAppsAo appsAo;
    private final MsConfigProperties properties;
    private final Utilities utilities;

    /**
     * Entry point for API-based SMS requests.
     * <p>
     * This method immediately returns a 202 Accepted response and processes the request
     * asynchronously on a separate thread. This approach prevents blocking the calling thread
     * and allows for better scalability.
     * <p>
     * The actual processing includes authentication, validation, and routing to the appropriate
     * integration channel based on the short code.
     *
     * @param smsRequest The SMS request containing recipient, message, and short code
     * @param authToken Authentication token for the request
     * @param reqReceiveTime Time when the request was received
     * @param headers HTTP headers from the original request
     * @return A Mono emitting a WsResponse with 202 Accepted status
     */
    public Mono<WsResponse> handleApiRequest(SmsRequest smsRequest, String authToken, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        // Create a defensive copy of headers
        Map<String, String> headersCopy = headers != null ? new HashMap<>(headers) : null;
        // Immediately return 202 Accepted response
        return responseMapper.setApiResponse(ERR_ACCEPTED, NULL, TRANS_ROUTER_ENTRY, FALSE, headers)
                .flatMap(data -> processApiRequest(smsRequest, authToken, reqReceiveTime, headersCopy)
                        // Process on a separate thread to avoid blocking
                        .subscribeOn(Schedulers.boundedElastic())
                        // Return the original 202 Accepted response
                        .then(Mono.just(data))
                );
    }

    /**
     * Entry point for ChatBot-based SMS requests.
     * <p>
     * Similar to apiRequestsEntry, this method immediately returns a 202 Accepted response
     * and processes the request asynchronously. It's designed to handle requests coming from
     * message queues rather than REST endpoints.
     *
     * @param chatBotRequest The ChatBot request containing recipient and message details
     * @param reqReceiveTime Time when the request was received
     * @param headers Headers or metadata associated with the request
     * @return A Mono emitting a WsResponse with 202 Accepted status
     */
    public Mono<WsResponse> handleChatBotRequest(ChatBotRequest chatBotRequest, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        // Immediately return 202 Accepted response
        return responseMapper.setApiResponse(ERR_ACCEPTED, NULL, TRANS_ROUTER_ENTRY, FALSE, headers)
                .flatMap(data -> processChatBotRequest(chatBotRequest, reqReceiveTime, headers)
                        // Process on a separate thread to avoid blocking
                        .subscribeOn(Schedulers.boundedElastic())
                        // Return the original 202 Accepted response
                        .then(Mono.just(data))
                );
    }

    /**
     * Processes an API-based SMS request.
     * <p>
     * This method authenticates the request by validating the auth token against the database,
     * checks if the token is authorized for the specified short code, and then delegates the
     * actual SMS processing to the SmsPreProcessor.
     *
     * @param smsRequest The SMS request to process
     * @param authToken Authentication token for the request
     * @param reqReceiveTime Time when the request was received
     * @param headers HTTP headers from the original request
     * @return A Mono emitting a WsResponse with the processing result
     */
    protected Mono<WsResponse> processApiRequest(SmsRequest smsRequest, String authToken, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        WsLogManager.starterDebug(reqReceiveTime.toString(), "processApiRequest", "0---", "Received");

        // Authenticate the request by looking up the app by auth token
        return appsAo.getAppByAuthToken(authToken)
                .flatMap(apps -> {
                    // Verify that the token is authorized for the specified short code
                    if (!apps.getAppSecretId().equalsIgnoreCase(smsRequest.getShortCode())) {
                        return Mono.error(new WsUnauthorizedException(SM_NOT_AUTHORIZED));
                    }
                    // Process the SMS request
                    return smsMessageProcessor.processSmsMessage(
                            apps.getAppSecretId(),
                            smsRequest.getCustomerPhoneNumber(),
                            smsRequest.getMessage(),
                            headers,
                            reqReceiveTime
                    );
                })
                // If no app is found for the token, return unauthorized error
                .switchIfEmpty(Mono.error(new WsUnauthorizedException(SM_NOT_AUTHORIZED)))
                // Handle any errors that occur during processing
                .onErrorResume(error -> utilities.handleExceptions(
                        headers,
                        reqReceiveTime,
                        ERR_SERVER_ERROR,
                        "processApiRequest",
                        TRANS_PROCESS_REQUEST,
                        error,
                        500
                ));
    }

    /**
     * Processes a ChatBot-based SMS request.
     * <p>
     * This method extracts the necessary information from the ChatBot request and delegates
     * the actual SMS processing to the SmsPreProcessor. Unlike API requests, ChatBot requests
     * don't require authentication as they're assumed to come from trusted internal sources.
     *
     * @param chatBotRequest The ChatBot request to process
     * @param reqReceiveTime Time when the request was received
     * @param headers Headers or metadata associated with the request
     * @return A Mono emitting a WsResponse with the processing result
     */
    protected Mono<WsResponse> processChatBotRequest(ChatBotRequest chatBotRequest, LocalDateTime reqReceiveTime, Map<String, String> headers) {
        // Validate that the recipient is not null
        if (chatBotRequest.getRecipient() == null) {
            return Mono.error(new BadRequestException("Recipient is NULL", ERR_BAD_REQUEST));
        }

        // Extract the necessary information from the ChatBot request
        String shortCode = chatBotRequest.getRecipient().getAppSecretId();
        String phoneNumber = chatBotRequest.getRecipient().getUserId();
        Message messagePayload = chatBotRequest.getMessage();

        // Process the SMS request
        return smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, messagePayload, headers, reqReceiveTime);
    }
}