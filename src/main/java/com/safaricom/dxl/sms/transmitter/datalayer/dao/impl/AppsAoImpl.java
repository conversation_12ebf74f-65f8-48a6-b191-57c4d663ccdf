package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;

import com.safaricom.dxl.sms.transmitter.datalayer.dao.IAppsAo;
import com.safaricom.dxl.sms.transmitter.datalayer.entities.Apps;
import com.safaricom.dxl.sms.transmitter.datalayer.repositories.AppsRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AppsAoImpl implements IAppsAo {
    private final AppsRepository appsRepository;

    public Mono<Apps> getAppByNameAndSecretId(String appName, String appSecretId) {
        return appsRepository.findByAppNameAndAppSecretId(appName, appSecretId);
    }

    public Mono<Apps> getAppByAuthToken(String authToken) {
        return appsRepository.findByAuthToken(authToken);
    }
}
