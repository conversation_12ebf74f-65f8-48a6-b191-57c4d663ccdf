package com.safaricom.dxl.sms.transmitter.utils;

public class GlobalVariables {

    public static final String ERR_SUCCESS = "200";
    public static final String ERR_ACCEPTED = "202";
    public static final String ERR_BAD_REQUEST = "400";
    public static final String ERR_UNAUTHORIZED = "401";
    public static final String ERR_INTEGRATION_ERROR = "501";
    public static final String ERR_SERVER_ERROR = "500";
    public static final String ERR_NO_STREAM = "1000";
    public static final String ERR_REDIS_GET = "CRE500500";
    public static final String ERR_REDIS_WRITE = "CRE500501";
    public static final String SERVICE_NAME = "zuri";
    public static final String TRANS_ROUTER_ENTRY = "ROUTER_ENTRY,SMS_ENTRY,SMS_TRANSMITTER";
    public static final String TRANS_ROUTER_PROCESSING = "ROUTER_PROCESSING,SMS_TRIAGE,SMS_TRANSMITTER";
    public static final String TRANS_PROCESS_REQUEST = "PROCESS_REQUEST,SMS_PROCESSOR,SMS_TRANSMITTER";
    public static final String TRANS_ROUTE_REQUEST = "ROUTE_REQUEST,REQUEST_ROUTER,SMS_TRANSMITTER";
    public static final String TRANS_MESSAGE_CONVERSION = "MESSAGE_CONVERSION,MESSAGE_CONVERSION,SMS_TRANSMITTER";
    public static final String TRANS_SEND_TIBCO_SMS = "TIBCO_SEND_SMS,TIBCO_SEND_SMS,TIBCO_INTEGRATION";
    public static final String TRANS_SEND_APACHE_SMS = "APACHE_SEND_SMS,APACHE_SEND_SMS,APACHE_INTEGRATION";
    public static final String TRANS_SEND_DXL_SMS = "DXL_SEND_SMS,DXL_SEND_SMS,DXL_INTEGRATION";
    public static final String TRANS_PROCESS_CACHE = "PROCESS_CACHE,SEND_SMS,SMS_TRANSMITTER";
    public static final String TRANS_GET_TOKEN = "GET_TOKEN,GET_TOKEN,APACHE_AUTH_TOKEN";
    public static final String TRANS_STREAM_SERVICE = "SMS_STREAM,SMS_STREAM,STREAM_SERVICE";
    public static final String TRANS_KAFKA_STREAM = "KAFKA_STREAM,KAFKA_STREAM,KAFKA";
    public static final String TRANS_DB_STREAM = "DB_STREAM,DB_STREAM,MONGO_DB";
    public static final String TRANS_KAFKA_STREAM_ZURI = "KAFKA_STREAM,KAFKA_STREAM_ZURI,KAFKA";
    public static final String TRANS_KAFKA_STREAM_DUBI = "KAFKA_STREAM,KAFKA_STREAM_DUBI,KAFKA";
    public static final String TRANS_DB_STREAM_ZURI = "DB_STREAM,DB_STREAM_ZURI,MONGO_DB";
    public static final String TRANS_DB_STREAM_DUBI = "DB_STREAM,DB_STREAM_DUBI,MONGO_DB";
    public static final String OUTBOUND = "OUTBOUND";
    public static final String DEFAULT_ERR_RESP = "Sorry but I've experienced a challenge responding to your message. Kindly repeat your message again.";
    public static final String LOCAL_TIME_ZONE = "Africa/Nairobi";
    GlobalVariables() {
    }

}
