package com.safaricom.dxl.sms.transmitter.model.dto;

import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.model.pojo.Recipient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Represents a request from the ChatBot system to send an SMS.
 * <p>
 * Contains all the necessary information to send an SMS, including the recipient details
 * and message content. This is used for messages coming from the RabbitMQ queue.
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class ChatBotRequest {

    @NotBlank(message = "Correlation ID is required")
    @Pattern(regexp = "^[A-Za-z0-9-]+$", message = "Correlation ID can only contain letters, numbers, and hyphens")
    @Size(max = 50, message = "Correlation ID cannot exceed 50 characters")
    private String correlationId;

    @NotNull(message = "Recipient is required")
    @Valid
    private Recipient recipient;

    @NotNull(message = "Message is required")
    @Valid
    private Message message;
}
