package com.safaricom.dxl.sms.transmitter.service;

import com.safaricom.dxl.sms.transmitter.datalayer.dao.IMenuForUserAo;
import com.safaricom.dxl.sms.transmitter.datalayer.entities.MenuForUser;
import com.safaricom.dxl.sms.transmitter.exception.BadRequestException;
import com.safaricom.dxl.sms.transmitter.exception.InternalServerErrorException;
import com.safaricom.dxl.sms.transmitter.model.pojo.Menu;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.model.pojo.MessageFormat;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;

@Service
@RequiredArgsConstructor
public class MessageConversionService {
    private static final String TARGET_SYSTEM = "SMS_TRANSMITTER";
    private static final String PROCESS_PREPARE_MESSAGE_FORMAT = "prepareMessageFormat";
    private final IMenuForUserAo menuForUserAo;
    private final Utilities utilities;

    /**
     * Converts a menu format to text representation.
     *
     * @param header the header text to display before the menu
     * @param shortCode the short code of the integration
     * @param buttons the list of buttons to convert to text
     * @param phoneNumber the phone number of the user
     * @param headers the request headers
     * @param reqReceiveTime the time the request was received
     * @return a Mono emitting the text representation of the menu
     */
    public Mono<String> convertMenuToTextFormat(String header, String shortCode, List<?> buttons, String phoneNumber, Map<String, String> headers, LocalDateTime reqReceiveTime) {
        List<Menu> menuList = createMenu(buttons);
        return saveMenu(phoneNumber, shortCode, menuList, headers, reqReceiveTime)
                .flatMap(menuForUser -> buttonsToText(header, menuList))
                .switchIfEmpty(Mono.error(new InternalServerErrorException(
                        "Failed to save or convert menu for user " + phoneNumber,
                        ERR_SERVER_ERROR)))
                .onErrorResume(error -> handleConversionError(error, headers, reqReceiveTime, phoneNumber, shortCode));
    }

    /**
     * Handles errors that occur during menu conversion.
     * Logs the error and returns an appropriate error response.
     *
     * @param error the error that occurred
     * @param headers the request headers
     * @param reqReceiveTime the time the request was received
     * @param phoneNumber the phone number of the user
     * @param shortCode the short code of the integration
     * @return a Mono that emits an error with appropriate context
     */
    protected Mono<String> handleConversionError(Throwable error, Map<String, String> headers, LocalDateTime reqReceiveTime,
                                                String phoneNumber, String shortCode) {
        String errorMsg = "Error converting menu format for user " + phoneNumber + " with shortcode " + shortCode + ": " + error.getMessage();
        utilities.logMessage(500, "convertMenuFormatToText", errorMsg, TARGET_SYSTEM, headers, LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE)), 500);
        return Mono.error(new InternalServerErrorException(errorMsg, ERR_SERVER_ERROR));
    }

    /**
     * Saves the menu for the given user and short code. If a menu for the given user and short code already exists, it is updated.
     * If no menu exists, a new menu is created.
     *
     * @param phoneNumber the phone number of the user
     * @param shortCode   the short code of the integration
     * @param menuList    the list of menus to be saved
     * @param headers     the request headers
     * @param reqReceiveTime the time the request was received
     * @return a Mono emitting the saved or updated MenuForUser
     */
    private Mono<MenuForUser> saveMenu(String phoneNumber, String shortCode, List<Menu> menuList, Map<String, String> headers, LocalDateTime reqReceiveTime) {
        return menuForUserAo.getMenuByUserIdAndShortCode(phoneNumber, shortCode)
                .flatMap(menuForUser -> {
                    menuForUser.setMenu(menuList);
                    return menuForUserAo.update(menuForUser)
                            .flatMap(Mono::just)
                            .onErrorResume(error -> {
                                String errorMsg = "Failed to update menu for user " + phoneNumber + ": " + error.getMessage();
                                utilities.logMessage(500, "updateMenu", errorMsg, TARGET_SYSTEM, headers, reqReceiveTime, 500);
                                return Mono.error(new InternalServerErrorException(errorMsg, ERR_SERVER_ERROR));
                            });
                })
                .switchIfEmpty(saveOrUpdateMenu(phoneNumber, shortCode, menuList, headers, reqReceiveTime))
                .onErrorResume(error -> {
                    // Only log the error if it's not already an InternalServerErrorException to avoid duplicate logging
                    if (!(error instanceof InternalServerErrorException)) {
                        String errorMsg = "Failed to save menu for user " + phoneNumber + ": " + error.getMessage();
                        utilities.logMessage(500, "saveMenu", errorMsg, TARGET_SYSTEM, headers, reqReceiveTime, 500);
                        return Mono.error(new InternalServerErrorException(errorMsg, ERR_SERVER_ERROR));
                    }
                    return Mono.error(error);
                });
    }

    /**
     * Creates and saves a new menu for the user.
     *
     * @param phoneNumber the phone number of the user
     * @param shortCode   the short code of the integration
     * @param menuList    the list of menus to be saved
     * @param headers     the request headers
     * @param reqReceiveTime the time the request was received
     * @return a Mono emitting the saved MenuForUser
     */
    private Mono<MenuForUser> saveOrUpdateMenu(String phoneNumber, String shortCode, List<Menu> menuList, Map<String, String> headers, LocalDateTime reqReceiveTime) {
        MenuForUser defaultMenuForUser = MenuForUser.builder()
                .userId(phoneNumber)
                .shortCode(shortCode)
                .menu(menuList)
                .available(true)
                .created(LocalDateTime.now())
                .build();
        return menuForUserAo.add(defaultMenuForUser)
                .flatMap(Mono::just)
                .onErrorResume(error -> {
                    String errorMsg = "Failed to add new menu for user " + phoneNumber + ": " + error.getMessage();
                    utilities.logMessage(500, "addMenu", errorMsg, TARGET_SYSTEM, headers, reqReceiveTime, 500);
                    return Mono.error(new InternalServerErrorException(errorMsg, ERR_SERVER_ERROR));
                });
    }

    /**
     * Prepares a message format based on the message payload.
     * Validates the message content and converts it to the appropriate format.
     *
     * @param messagePayload the message payload to format
     * @param headers the request headers
     * @param reqReceiveTime the time the request was received
     * @return the formatted message
     * @throws BadRequestException if the message format is invalid
     * @throws InternalServerErrorException if there is an error processing the message
     */
    public MessageFormat prepareMessageFormat(Message messagePayload, Map<String, String> headers, LocalDateTime reqReceiveTime) {
        try {
            validateMessageType(messagePayload);
            String textType = messagePayload.getType();

            if (textType.equalsIgnoreCase("text")) {
                return processTextMessage(messagePayload);
            } else if (textType.equalsIgnoreCase("buttons")) {
                return processButtonsMessage(messagePayload);
            } else {
                throw new BadRequestException("Unsupported message type: " + textType, ERR_BAD_REQUEST);
            }
        } catch (NullPointerException nPE) {
            return handleMessageFormatError(nPE, "Null pointer exception while preparing message format", 500, headers, reqReceiveTime);
        } catch (ClassCastException cce) {
            String errorMsg = "Invalid data type in message payload: " + cce.getMessage();
            utilities.logMessage(400, PROCESS_PREPARE_MESSAGE_FORMAT, errorMsg, TARGET_SYSTEM, headers, reqReceiveTime, 400);
            throw new BadRequestException(errorMsg, ERR_BAD_REQUEST);
        } catch (BadRequestException bre) {
            // Re-throw BadRequestException as is
            throw bre;
        } catch (Exception e) {
            return handleMessageFormatError(e, "Unexpected error while preparing message format", 500, headers, reqReceiveTime);
        }
    }

    /**
     * Validates that the message type is not null.
     *
     * @param messagePayload the message payload to validate
     * @throws BadRequestException if the message type is null
     */
    private void validateMessageType(Message messagePayload) {
        if (messagePayload.getType() == null) {
            throw new BadRequestException("Message type cannot be null", ERR_BAD_REQUEST);
        }
    }

    /**
     * Processes a text message and returns a MessageFormat.
     *
     * @param messagePayload the message payload to process
     * @return the formatted message
     * @throws BadRequestException if the message format is invalid
     */
    private MessageFormat processTextMessage(Message messagePayload) {
        validateTextMessageDetails(messagePayload);
        String textDetails = (String) messagePayload.getDetails();
        return new MessageFormat(new ArrayList<>(), textDetails);
    }

    /**
     * Validates the details of a text message.
     *
     * @param messagePayload the message payload to validate
     * @throws BadRequestException if the message details are invalid
     */
    private void validateTextMessageDetails(Message messagePayload) {
        if ((messagePayload.getDetails() instanceof List<?>)) {
            throw new BadRequestException("Details Type is invalid for text message", ERR_BAD_REQUEST);
        }
        if (messagePayload.getDetails() == null) {
            throw new BadRequestException("Message details cannot be null for text message", ERR_BAD_REQUEST);
        }
        if (checkUnwantedCharacters((String) messagePayload.getDetails())) {
            throw new BadRequestException("Details value contains invalid characters", ERR_BAD_REQUEST);
        }
    }

    /**
     * Processes a buttons message and returns a MessageFormat.
     *
     * @param messagePayload the message payload to process
     * @return the formatted message
     * @throws BadRequestException if the message format is invalid
     */
    private MessageFormat processButtonsMessage(Message messagePayload) {
        List<?> buttons = validateButtonsMessageDetails(messagePayload);
        validateButtonsContent(buttons);
        validateButtonsTitle(messagePayload);
        return new MessageFormat(buttons, messagePayload.getTitle());
    }

    /**
     * Validates the details of a buttons message.
     *
     * @param messagePayload the message payload to validate
     * @return the list of buttons
     * @throws BadRequestException if the message details are invalid
     */
    private List<?> validateButtonsMessageDetails(Message messagePayload) {
        if ((messagePayload.getDetails() instanceof String)) {
            throw new BadRequestException("Details Type is invalid for buttons message", ERR_BAD_REQUEST);
        }
        if (messagePayload.getDetails() == null) {
            throw new BadRequestException("Message details cannot be null for buttons message", ERR_BAD_REQUEST);
        }
        List<?> buttons = (List<?>) messagePayload.getDetails();
        if (buttons.isEmpty()) {
            throw new BadRequestException("Buttons list cannot be empty", ERR_BAD_REQUEST);
        }
        return buttons;
    }

    /**
     * Validates the content of buttons.
     *
     * @param buttons the buttons to validate
     * @throws BadRequestException if any button is invalid
     */
    private void validateButtonsContent(List<?> buttons) {
        buttons.forEach(button -> {
            if (button == null) {
                throw new BadRequestException("Button value cannot be null", ERR_BAD_REQUEST);
            }
            if (checkUnwantedCharacters((String) button)) {
                throw new BadRequestException("Button value contains invalid characters: " + button, ERR_BAD_REQUEST);
            }
        });
    }

    /**
     * Validates the title of a buttons message.
     *
     * @param messagePayload the message payload to validate
     * @throws BadRequestException if the title is invalid
     */
    private void validateButtonsTitle(Message messagePayload) {
        if (messagePayload.getTitle() == null || messagePayload.getTitle().trim().isEmpty()) {
            throw new BadRequestException("Title cannot be null or empty for buttons message", ERR_BAD_REQUEST);
        }
    }

    /**
     * Handles errors that occur during message format preparation.
     *
     * @param error the error that occurred
     * @param errorPrefix the prefix for the error message
     * @param errorCode the error code
     * @param headers the request headers
     * @param reqReceiveTime the time the request was received
     * @return never returns, always throws an exception
     * @throws InternalServerErrorException with the formatted error message
     */
    private MessageFormat handleMessageFormatError(Throwable error, String errorPrefix, int errorCode, Map<String, String> headers, LocalDateTime reqReceiveTime) {
        String errorMsg = errorPrefix + ": " + error.getMessage();
        utilities.logMessage(errorCode, PROCESS_PREPARE_MESSAGE_FORMAT, errorMsg, TARGET_SYSTEM, headers, reqReceiveTime, errorCode);
        throw new InternalServerErrorException(errorMsg, ERR_SERVER_ERROR);
    }

    public boolean checkUnwantedCharacters(String input) {
        String safeCharactersRegex = "^[\\w\\s\\p{Punct}]*$";
        Pattern safeCharactersPattern = Pattern.compile(safeCharactersRegex);
        if (input == null || input.isEmpty()) {
            return true;
        }
        Matcher matcher = safeCharactersPattern.matcher(input);
        return !matcher.matches();
    }

    /**
     * Resolve issue affecting ISO format used by Tibco's SMPP client.
     * Prevents customers from receiving encoded character representation
     * for the right single quotation mark (’).
     */
    public Mono<String> decodeSpecialCharacters(String text) {
        String decodedText = text.replace("’", "'");
        return Mono.just(decodedText);
    }

    /**
     * Creates a list of menus based on the given buttons.
     *
     * @param buttons the buttons to be used to create the menus
     * @return the list of menus
     */
    private List<Menu> createMenu(List<?> buttons) {
        List<Menu> menuList = new ArrayList<>();
        int counter = 1;
        while (counter <= (buttons.size())) {
            if (buttons.get(counter - 1).toString().equalsIgnoreCase("Main Menu")) {
                menuList.add(new Menu("0", buttons.get((counter - 1)).toString()));
            } else if (buttons.get(counter - 1).toString().equalsIgnoreCase("Exit")) {
                menuList.add(new Menu("00", buttons.get((counter - 1)).toString()));
            } else {
                menuList.add(new Menu(String.valueOf(counter), buttons.get((counter - 1)).toString()));
            }
            counter++;
        }
        return menuList;
    }

    /**
     * Creates a text representation of the buttons based on the header and the list of menus.
     *
     * @param header   the header to be displayed before the buttons
     * @param menuList the list of menus to be displayed as buttons
     * @return the text representation of the buttons
     */
    private Mono<String> buttonsToText(String header, List<Menu> menuList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(header);
        stringBuilder.append("\n");
        String newline = "";
        for (Menu menu : menuList) {
            stringBuilder.append(newline).append(menu.getHolder()).append(". ").append(menu.getValue());
            newline = "\n";
        }
        return Mono.just(stringBuilder.toString());
    }
}
