package com.safaricom.dxl.sms.transmitter.entry.controller;

import com.safaricom.dxl.sms.transmitter.model.dto.SmsRequest;
import com.safaricom.dxl.sms.transmitter.service.SmsRequestRouter;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.LOCAL_TIME_ZONE;

/**
 * REST controller for handling SMS transmission requests (API v2).
 * <p>
 * This controller provides endpoints for sending SMS messages through the v2 API.
 * It uses header-based authentication where the auth token is provided in the 'x-app-id' header.
 * <p>
 * The controller delegates the actual processing to the RequestRouter service,
 * which handles routing to the appropriate integration channel based on the short code.
 * <p>
 * This version of the API is more modern and follows better RESTful practices compared to v1.
 *
 * <AUTHOR>
 */
@Tag(name = "Ms SMS Transmitter", description = "SMS transmission endpoints")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v2/integrations")
public class SendSmsControllerV2 {
    private final SmsRequestRouter smsRequestRouter;

    /**
     * Sends an SMS message using the authentication token from headers and SMS request details.
     * <p>
     * This endpoint accepts an SMS request containing the recipient's phone number,
     * the message content, and the short code. The auth token is extracted from the
     * 'x-app-id' header and must match the configured token for the specified short code.
     * <p>
     * The endpoint returns immediately with a 202 Accepted response, and the actual
     * SMS processing happens asynchronously.
     * <p>
     * This v2 endpoint follows better RESTful practices by using headers for authentication
     * instead of path variables.
     *
     * @param smsRequest Request object containing recipient phone number, message content, and short code
     * @param headers HTTP request headers containing authentication token and other metadata
     * @return A Mono emitting a WsResponse with the status of the request acceptance
     */
    @Operation(summary = "Send SMS", description = "Sends an SMS message to the specified recipient")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "202", description = "Request accepted for processing"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
        @ApiResponse(responseCode = "401", description = "Authentication failed"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/send")
    public Mono<WsResponse> sendSms(
            @Parameter(description = "SMS request details", required = true, content = @Content(schema = @Schema(implementation = SmsRequest.class)))
            @Valid @RequestBody SmsRequest smsRequest,
            @RequestHeader Map<String, String> headers) {

        // Record the time the request was received for metrics and logging
        LocalDateTime receiveTime = LocalDateTime.now(ZoneId.of(LOCAL_TIME_ZONE));

        // Extract the authentication token from the x-app-id header
        String authToken = headers.getOrDefault("x-app-id", "");

        // Delegate to the request router for processing
        return smsRequestRouter.handleApiRequest(smsRequest, authToken, receiveTime, headers);
    }
}