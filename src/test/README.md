# Testing Strategy for ms-sms-transmitter

This document outlines the testing strategy for the ms-sms-transmitter microservice, focusing on comprehensive unit testing of the service layer.

## Test Structure

The test directory structure mirrors the main source code structure:

```
src/test/java/com/safaricom/dxl/sms/transmitter/
├── service/
│   ├── impl/
│   │   ├── StreamingServiceTest.java
│   │   ├── SmsMessageProcessorTest.java
│   │   ├── SmsRequestRouterTest.java
│   │   └── MessageConversionServiceTest.java
│   └── integration/
│       └── impl/
│           ├── TibcoIntegrationProcessorTest.java
│           ├── ApacheIntegrationProcessorTest.java
│           └── TibcoApacheApiRespProcessorTest.java
└── stub/
    └── Stub.java
```

## Testing Approach

### 1. Unit Testing

Unit tests focus on testing individual components in isolation, with dependencies mocked. Key principles:

- **Isolation**: Each test focuses on a single method or behavior
- **Mocking**: External dependencies are mocked using Mockito
- **Comprehensive Coverage**: Tests cover both happy paths and error scenarios
- **Reactive Testing**: StepVerifier is used to test reactive streams (Mono/Flux)

### 2. Test Data Generation

Test data is centralized in the `Stub` class, which provides static methods to create mock objects for testing. This approach:

- Reduces duplication across test classes
- Ensures consistency in test data
- Makes tests more readable and maintainable

### 3. Test Organization

Tests are organized using JUnit 5's nested classes, with each nested class focusing on a specific method or group of related methods. This approach:

- Improves test readability
- Groups related tests together
- Allows for shared setup within a group of tests

### 4. Naming Convention

Tests follow the naming convention: `methodName_scenario_expectedOutcome`, which clearly communicates:

- What method is being tested
- Under what conditions
- What the expected result is

### 5. Test Structure

Each test follows the Arrange-Act-Assert pattern:

```java
@Test
void methodName_scenario_expectedOutcome() {
    // Arrange - Set up test data and mock behavior
    when(dependency.method()).thenReturn(expectedResult);
    
    // Act - Call the method under test
    Mono<Result> result = classUnderTest.methodUnderTest();
    
    // Assert - Verify the results
    StepVerifier.create(result)
        .expectNextMatches(response -> /* assertions */)
        .verifyComplete();
}
```

## Key Components Tested

### 1. StreamingService

Tests focus on:
- Streaming SMS metrics to Kafka
- Streaming SMS metrics to MongoDB
- Error handling
- Conditional logic based on configuration

### 2. SmsMessageProcessor

Tests focus on:
- Input validation
- Message formatting
- Routing to the appropriate processor
- Error handling and graceful degradation

### 3. SmsRequestRouter

Tests focus on:
- Authentication and authorization
- Request validation
- Asynchronous processing
- Error handling

### 4. MessageConversionService

Tests focus on:
- Converting structured messages to text
- Menu formatting
- Character encoding/decoding
- Error handling

## Running Tests

Tests can be run using Maven:

```bash
mvn test
```

Or for a specific test class:

```bash
mvn test -Dtest=StreamingServiceTest
```

## Test Coverage

The test suite aims to achieve high code coverage, focusing on:

- Service layer components
- Business logic
- Error handling
- Edge cases

Excluded from coverage requirements (as specified in pom.xml):
- Configuration classes
- Exception classes
- Model classes
- Application entry point
