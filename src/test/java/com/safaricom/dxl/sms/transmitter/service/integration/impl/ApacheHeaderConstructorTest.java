package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.AuthTokenProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ApacheHeaderConstructorTest {

    @Mock
    private AuthTokenProcessor authTokenProcessor;

    @InjectMocks
    private ApacheHeaderConstructor headerConstructor;

    private String destinationAddress;
    private Map<String, String> headers;
    private String authToken;

    @BeforeEach
    void setUp() {
        destinationAddress = "254722000000";
        headers = new HashMap<>();
        headers.put("X-Conversation-ID", "test-conversation-id");
        authToken = "Bearer test-token";

        when(authTokenProcessor.getAuthToken(any())).thenReturn(Mono.just(authToken));
    }

    @Test
    void constructHeaders_shouldCreateCorrectHeaders() {
        // Act
        Mono<Consumer<HttpHeaders>> result = headerConstructor.constructHeaders(destinationAddress, headers);

        // Assert
        StepVerifier.create(result)
                .expectNextMatches(consumer -> {
                    HttpHeaders httpHeaders = new HttpHeaders();
                    consumer.accept(httpHeaders);
                    
                    assertEquals(MediaType.APPLICATION_JSON, httpHeaders.getContentType());
                    assertEquals(authToken, httpHeaders.getFirst(HttpHeaders.AUTHORIZATION));
                    
                    return true;
                })
                .verifyComplete();
    }

    @Test
    void constructHeaders_whenAuthTokenFails_shouldPropagateError() {
        // Arrange
        when(authTokenProcessor.getAuthToken(any())).thenReturn(Mono.error(new RuntimeException("Auth error")));

        // Act & Assert
        StepVerifier.create(headerConstructor.constructHeaders(destinationAddress, headers))
                .expectErrorMatches(throwable -> throwable instanceof RuntimeException && 
                                   throwable.getMessage().equals("Auth error"))
                .verify();
    }
}
