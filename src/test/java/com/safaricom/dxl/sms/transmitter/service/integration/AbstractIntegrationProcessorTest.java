package com.safaricom.dxl.sms.transmitter.service.integration;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AbstractIntegrationProcessorTest {

    @Mock
    private HttpRequestHandler httpRequestHandler;

    private TestIntegrationProcessor processor;
    private final String sourceAddress = "12345";
    private final String destinationAddress = "254722000000";
    private final String message = "Test message";
    private final LocalDateTime requestTime = LocalDateTime.now();
    private final Map<String, String> headers = new HashMap<>();
    private final String endpoint = "https://test-endpoint.com";
    private final String payload = "Test payload";
    private final Consumer<HttpHeaders> httpHeadersConsumer = httpHeaders -> httpHeaders.add("Authorization", "Bearer test-token");
    private final String response = "Test response";
    private final WsResponse wsResponse = Stub.mockWsResponse("200", "Success", null);

    @BeforeEach
    void setUp() {
        headers.put("X-Conversation-ID", "test-conversation-id");
        processor = new TestIntegrationProcessor(httpRequestHandler);
    }

    @Nested
    class ProcessIntegrationRequestTests {

        @Test
        void processIntegrationRequest_withValidInputs_shouldReturnSuccessResponse() {
            // Arrange
            when(httpRequestHandler.post(eq(endpoint), any(), eq(payload), eq(String.class)))
                    .thenReturn(Mono.just(response));

            // Act
            WsResponse result = processor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            assertNotNull(result);
            assertEquals(200, result.getHeader().getResponseCode());

            // Verify all steps were called
            verify(httpRequestHandler).post(eq(endpoint), any(), eq(payload), eq(String.class));
        }

        @Test
        void processIntegrationRequest_whenHttpRequestFails_shouldHandleError() {
            // Arrange
            RuntimeException exception = new RuntimeException("HTTP request failed");
            when(httpRequestHandler.post(eq(endpoint), any(), eq(payload), eq(String.class)))
                    .thenReturn(Mono.error(exception));

            // Act
            WsResponse result = processor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            assertNotNull(result);
            assertEquals(500, result.getHeader().getResponseCode());
            System.out.println("Response message: " + result.getHeader().getResponseMessage());
            // Just check that we have a response message
            assertNotNull(result.getHeader().getResponseMessage());
        }

        @Test
        void processIntegrationRequest_whenResponseProcessingFails_shouldHandleError() {
            // Arrange
            when(httpRequestHandler.post(eq(endpoint), any(), eq(payload), eq(String.class)))
                    .thenReturn(Mono.just("Invalid response"));
            processor.setFailResponseProcessing(true);

            // Act
            WsResponse result = processor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            assertNotNull(result);
            assertEquals(500, result.getHeader().getResponseCode());
            System.out.println("Response message: " + result.getHeader().getResponseMessage());
            // Just check that we have a response message
            assertNotNull(result.getHeader().getResponseMessage());
        }

        @Test
        void processIntegrationRequest_whenHeaderConstructionFails_shouldHandleError() {
            // Arrange
            processor.setFailHeaderConstruction(true);

            // Act
            WsResponse result = processor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            assertNotNull(result);
            assertEquals(500, result.getHeader().getResponseCode());
            System.out.println("Response message: " + result.getHeader().getResponseMessage());
            // Just check that we have a response message
            assertNotNull(result.getHeader().getResponseMessage());
        }
    }

    /**
     * Concrete implementation of AbstractIntegrationProcessor for testing.
     */
    private class TestIntegrationProcessor extends AbstractIntegrationProcessor<String, String> {

        private boolean failHeaderConstruction = false;
        private boolean failResponseProcessing = false;

        public TestIntegrationProcessor(HttpRequestHandler httpRequestHandler) {
            super(httpRequestHandler);
        }

        public void setFailHeaderConstruction(boolean failHeaderConstruction) {
            this.failHeaderConstruction = failHeaderConstruction;
        }

        public void setFailResponseProcessing(boolean failResponseProcessing) {
            this.failResponseProcessing = failResponseProcessing;
        }

        @Override
        protected String generatePayload(String sourceAddress, String destinationAddress, String message, Map<String, String> headers) {
            return payload;
        }

        @Override
        protected String getEndpoint() {
            return endpoint;
        }

        @Override
        protected Mono<Consumer<HttpHeaders>> constructHeaders(String destinationAddress, Map<String, String> headers) {
            if (failHeaderConstruction) {
                return Mono.error(new RuntimeException("Header construction failed"));
            }
            return Mono.just(httpHeadersConsumer);
        }

        @Override
        protected Class<String> getResponseClass() {
            return String.class;
        }

        @Override
        protected Mono<WsResponse> processResponse(String response, String sourceAddress, String destinationAddress,
                                                String endpoint, String message, LocalDateTime reqReceiveTime,
                                                Map<String, String> headers) {
            if (failResponseProcessing) {
                return Mono.error(new RuntimeException("Response processing failed"));
            }
            return Mono.just(wsResponse);
        }

        @Override
        protected Mono<WsResponse> handleError(Throwable error, String sourceAddress, String destinationAddress,
                                            String message, LocalDateTime reqReceiveTime, Map<String, String> headers) {
            return Mono.just(Stub.mockWsResponse("500", "Error handled: " + error.getMessage(), null));
        }
    }
}
