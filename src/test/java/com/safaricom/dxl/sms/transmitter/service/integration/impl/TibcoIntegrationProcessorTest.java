package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.TibcoMessage;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Response;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_INTEGRATION_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.TRANS_SEND_TIBCO_SMS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TibcoIntegrationProcessorTest {

    @Mock
    private TibcoPayloadConstructor payloadConstructor;

    @Mock
    private TibcoHeaderConstructor headerConstructor;

    @Mock
    private MsConfigProperties properties;

    @Mock
    private HttpRequestHandler httpRequestHandler;

    @Mock
    private TibcoApacheApiRespProcessor apiRespProcessor;

    @Mock
    private Utilities utilities;

    @InjectMocks
    private TibcoIntegrationProcessor tibcoIntegrationProcessor;

    private String sourceAddress;
    private String destinationAddress;
    private String message;
    private Map<String, String> headers;
    private LocalDateTime requestTime;
    private TibcoMessage tibcoMessage;
    private Response response;
    private Consumer<HttpHeaders> httpHeadersConsumer;

    @BeforeEach
    void setUp() {
        sourceAddress = "12345";
        destinationAddress = "254722000000";
        message = "Test message";
        headers = Stub.mockHeaders();
        requestTime = Stub.mockDateTime();
        tibcoMessage = Stub.mockTibcoMessage();
        response = Stub.mockIntegrationResponse("00");
        httpHeadersConsumer = httpHeaders -> {};

        when(properties.getTibcoEndpoint()).thenReturn("https://test-tibco-endpoint.com");
        when(payloadConstructor.generatePayload(anyString(), anyString(), anyString(), any())).thenReturn(tibcoMessage);
        when(headerConstructor.constructHeaders(anyString(), any())).thenReturn(Mono.just(httpHeadersConsumer));
        when(httpRequestHandler.post(anyString(), any(), any(), eq(Response.class))).thenReturn(Mono.just(response));
        when(apiRespProcessor.processApiResponse(any(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.just(Stub.mockWsResponse("200", "Success", null)));
        when(utilities.handleExceptions(any(), any(), anyString(), anyString(), anyString(), any(), anyInt()))
                .thenReturn(Mono.just(Stub.mockWsResponse("500", "Error", null)));
    }

    @Nested
    class ProcessIntegrationRequestTests {

        @Test
        void processIntegrationRequest_withValidRequest_shouldCallDependencies() {
            // Act
            tibcoIntegrationProcessor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            Mockito.verify(payloadConstructor).generatePayload(sourceAddress, destinationAddress, message, headers);
            Mockito.verify(headerConstructor).constructHeaders(destinationAddress, headers);
            Mockito.verify(httpRequestHandler).post(anyString(), any(), any(), eq(Response.class));
            Mockito.verify(apiRespProcessor).processApiResponse(any(), anyString(), anyString(), anyString(), anyString(), any(), any());
        }

        @Test
        void processIntegrationRequest_whenHttpRequestFails_shouldHandleError() {
            // Arrange
            when(httpRequestHandler.post(anyString(), any(), any(), eq(Response.class)))
                    .thenReturn(Mono.error(new RuntimeException("HTTP request failed")));

            // Act
            tibcoIntegrationProcessor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            Mockito.verify(utilities).handleExceptions(
                    any(), any(), anyString(), anyString(), anyString(), any(RuntimeException.class), anyInt());
        }
    }

    @Nested
    class GeneratePayloadTests {

        @Test
        void generatePayload_shouldDelegateToPayloadConstructor() {
            // Act
            TibcoMessage result = tibcoIntegrationProcessor.generatePayload(sourceAddress, destinationAddress, message, headers);

            // Assert
            assertEquals(tibcoMessage, result);
            Mockito.verify(payloadConstructor).generatePayload(sourceAddress, destinationAddress, message, headers);
        }
    }

    @Nested
    class GetEndpointTests {

        @Test
        void getEndpoint_shouldReturnConfiguredEndpoint() {
            // Act
            String result = tibcoIntegrationProcessor.getEndpoint();

            // Assert
            assertEquals("https://test-tibco-endpoint.com", result);
            Mockito.verify(properties).getTibcoEndpoint();
        }
    }

    @Nested
    class ConstructHeadersTests {

        @Test
        void constructHeaders_shouldDelegateToHeaderConstructor() {
            // Act
            Mono<Consumer<HttpHeaders>> result = tibcoIntegrationProcessor.constructHeaders(destinationAddress, headers);

            // Assert
            StepVerifier.create(result)
                    .expectNext(httpHeadersConsumer)
                    .verifyComplete();
            Mockito.verify(headerConstructor).constructHeaders(destinationAddress, headers);
        }
    }

    @Nested
    class GetResponseClassTests {

        @Test
        void getResponseClass_shouldReturnResponseClass() {
            // Act
            Class<Response> result = tibcoIntegrationProcessor.getResponseClass();

            // Assert
            assertEquals(Response.class, result);
        }
    }

    @Nested
    class ProcessResponseTests {

        @Test
        void processResponse_shouldDelegateToApiRespProcessor() {
            // Act
            tibcoIntegrationProcessor.processResponse(
                    response, sourceAddress, destinationAddress, "endpoint", message, requestTime, headers);

            // Assert
            Mockito.verify(apiRespProcessor).processApiResponse(
                    response, sourceAddress, destinationAddress, "endpoint", message, requestTime, headers);
        }
    }

    @Nested
    class HandleErrorTests {

        @Test
        void handleError_shouldDelegateToUtilities() {
            // Arrange
            RuntimeException error = new RuntimeException("Test error");

            // Act
            tibcoIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers);

            // Assert
            Mockito.verify(utilities).handleExceptions(
                    headers, requestTime, ERR_INTEGRATION_ERROR, "Tibco API Call", TRANS_SEND_TIBCO_SMS, error, 500);
        }
    }
}
