package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.Specification;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.CharacteristicValue;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Parts;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Response;
import com.safaricom.dxl.sms.transmitter.service.StreamingService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_INTEGRATION_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_SUCCESS;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TibcoApacheApiRespProcessorTest {

    @Mock
    private StreamingService streamingService;

    @Mock
    private WsResponseMapper responseMapper;

    @Mock
    private MsConfigProperties properties;

    @InjectMocks
    private TibcoApacheApiRespProcessor apiRespProcessor;

    private Response response;
    private String sourceAddress;
    private String destinationAddress;
    private String endPoint;
    private String message;
    private LocalDateTime reqReceiveTime;
    private Map<String, String> headers;
    private WsResponse successResponse;
    private WsResponse errorResponse;

    @BeforeEach
    void setUp() {
        // Set up test data
        sourceAddress = "12345";
        destinationAddress = "254722000000";
        endPoint = "https://test-endpoint.com";
        message = "Test message";
        reqReceiveTime = LocalDateTime.now();
        headers = new HashMap<>();
        headers.put(X_CONVERSATION_ID, "test-conversation-id");

        // Create a success response
        List<CharacteristicValue> characteristicValues = new ArrayList<>();
        characteristicValues.add(new CharacteristicValue("ResponseCode", "00"));
        characteristicValues.add(new CharacteristicValue("ResponseDescription", "Success"));

        Specification specification = new Specification();
        specification.setCharacteristicValue(characteristicValues);

        Parts parts = new Parts();
        parts.setSpecification(specification);

        response = new Response();
        response.setParts(parts);

        // Mock responses
        successResponse = Stub.mockWsResponse("200", "Success", null);
        errorResponse = Stub.mockWsResponse("500", "Error", null);

        // Mock properties
        when(properties.getTibcoUrlKeyword()).thenReturn("tibco");

        // Mock streaming service
        when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.just(successResponse));

        // Mock response mapper to return our predefined responses
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), eq(NULL), anyString(), anyString(), eq(FALSE), anyMap()))
                .thenReturn(Mono.just(successResponse));
        when(responseMapper.setApiResponse(eq(ERR_INTEGRATION_ERROR), eq(NULL), anyString(), anyString(), eq(FALSE), anyMap()))
                .thenReturn(Mono.just(errorResponse));
    }

    @Nested
    class ProcessApiResponseTests {

        @Test
        void processApiResponse_withSuccessResponseCode_shouldReturnSuccessResponse() {
            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endPoint, message, reqReceiveTime, headers))
                    .expectNext(successResponse)
                    .verifyComplete();
        }

        @Test
        void processApiResponse_withAlternativeSuccessResponseCode_shouldReturnSuccessResponse() {
            // Arrange
            response.getParts().getSpecification().getCharacteristicValue().get(0).setValue("1000");

            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endPoint, message, reqReceiveTime, headers))
                    .expectNext(successResponse)
                    .verifyComplete();
        }

        @Test
        void processApiResponse_withErrorResponseCode_shouldReturnErrorResponse() {
            // Arrange
            response.getParts().getSpecification().getCharacteristicValue().get(0).setValue("999");

            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endPoint, message, reqReceiveTime, headers))
                    .expectNext(errorResponse)
                    .verifyComplete();
        }

        @Test
        void processApiResponse_withTibcoEndpoint_shouldUseCorrectProcess() {
            // Arrange
            String tibcoEndpoint = "https://tibco-endpoint.com";

            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, tibcoEndpoint, message, reqReceiveTime, headers))
                    .expectNext(successResponse)
                    .verifyComplete();
        }

        @Test
        void processApiResponse_withApacheEndpoint_shouldUseCorrectProcess() {
            // Arrange
            String apacheEndpoint = "https://apache-endpoint.com";

            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, apacheEndpoint, message, reqReceiveTime, headers))
                    .expectNext(successResponse)
                    .verifyComplete();
        }

        @Test
        void processApiResponse_withMissingResponseCode_shouldUseDefaultCode() {
            // Arrange
            response.getParts().getSpecification().setCharacteristicValue(new ArrayList<>());

            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endPoint, message, reqReceiveTime, headers))
                    .expectNext(errorResponse)
                    .verifyComplete();
        }
    }
}
