package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.ApachePayload;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.TibcoMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class ApachePayloadConstructorTest {

    @InjectMocks
    private ApachePayloadConstructor payloadConstructor;

    private String shortCode;
    private String destinationAddress;
    private String message;
    private Map<String, String> headers;

    @BeforeEach
    void setUp() {
        shortCode = "12345";
        destinationAddress = "254722000000";
        message = "Test message";
        headers = new HashMap<>();
    }

    @Test
    void generatePayload_shouldCreateCorrectPayload() {
        // Act
        ApachePayload payload = payloadConstructor.generatePayload(shortCode, destinationAddress, message, headers);

        // Assert
        assertNotNull(payload);
        assertNotNull(payload.getRequest());
        assertEquals(1, payload.getRequest().size());
        
        TibcoMessage tibcoMessage = payload.getRequest().get(0);
        
        // Check roles
        assertNotNull(tibcoMessage.getRoles());
        assertNotNull(tibcoMessage.getRoles().getReceiver());
        assertNotNull(tibcoMessage.getRoles().getReceiver().getId());
        assertEquals(1, tibcoMessage.getRoles().getReceiver().getId().size());
        assertEquals(destinationAddress, tibcoMessage.getRoles().getReceiver().getId().get(0).getValue());
        
        // Check parts
        assertNotNull(tibcoMessage.getParts());
        assertNotNull(tibcoMessage.getParts().getBody());
        assertEquals(message, tibcoMessage.getParts().getBody().getText());
        assertNotNull(tibcoMessage.getParts().getTrailer());
        assertEquals(shortCode, tibcoMessage.getParts().getTrailer().getText());
    }
}
