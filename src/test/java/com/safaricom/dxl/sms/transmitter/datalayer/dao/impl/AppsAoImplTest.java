package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;

import com.safaricom.dxl.sms.transmitter.datalayer.entities.Apps;
import com.safaricom.dxl.sms.transmitter.datalayer.repositories.AppsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AppsAoImplTest {

    @Mock
    private AppsRepository appsRepository;

    @InjectMocks
    private AppsAoImpl appsAo;

    private Apps app;
    private final String appName = "test-app";
    private final String appSecretId = "12345";
    private final String authToken = "auth-token-123";

    @BeforeEach
    void setUp() {
        app = Stub.mockApp();
    }

    @Nested
    class GetAppByNameAndSecretIdTests {

        @Test
        void getAppByNameAndSecretId_withValidInput_shouldReturnApp() {
            // Arrange
            when(appsRepository.findByAppNameAndAppSecretId(appName, appSecretId)).thenReturn(Mono.just(app));

            // Act & Assert
            StepVerifier.create(appsAo.getAppByNameAndSecretId(appName, appSecretId))
                    .expectNext(app)
                    .verifyComplete();
        }

        @Test
        void getAppByNameAndSecretId_withNonExistingApp_shouldReturnEmpty() {
            // Arrange
            when(appsRepository.findByAppNameAndAppSecretId(appName, appSecretId)).thenReturn(Mono.empty());

            // Act & Assert
            StepVerifier.create(appsAo.getAppByNameAndSecretId(appName, appSecretId))
                    .verifyComplete();
        }

        @Test
        void getAppByNameAndSecretId_withError_shouldPropagateError() {
            // Arrange
            when(appsRepository.findByAppNameAndAppSecretId(appName, appSecretId))
                    .thenReturn(Mono.error(new RuntimeException("Database error")));

            // Act & Assert
            StepVerifier.create(appsAo.getAppByNameAndSecretId(appName, appSecretId))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Database error"))
                    .verify();
        }
    }

    @Nested
    class GetAppByAuthTokenTests {

        @Test
        void getAppByAuthToken_withValidInput_shouldReturnApp() {
            // Arrange
            when(appsRepository.findByAuthToken(authToken)).thenReturn(Mono.just(app));

            // Act & Assert
            StepVerifier.create(appsAo.getAppByAuthToken(authToken))
                    .expectNext(app)
                    .verifyComplete();
        }

        @Test
        void getAppByAuthToken_withNonExistingApp_shouldReturnEmpty() {
            // Arrange
            when(appsRepository.findByAuthToken(authToken)).thenReturn(Mono.empty());

            // Act & Assert
            StepVerifier.create(appsAo.getAppByAuthToken(authToken))
                    .verifyComplete();
        }

        @Test
        void getAppByAuthToken_withError_shouldPropagateError() {
            // Arrange
            when(appsRepository.findByAuthToken(authToken))
                    .thenReturn(Mono.error(new RuntimeException("Database error")));

            // Act & Assert
            StepVerifier.create(appsAo.getAppByAuthToken(authToken))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Database error"))
                    .verify();
        }
    }
}
