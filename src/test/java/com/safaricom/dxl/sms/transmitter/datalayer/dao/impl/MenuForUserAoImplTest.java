package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;

import com.safaricom.dxl.sms.transmitter.datalayer.entities.MenuForUser;
import com.safaricom.dxl.sms.transmitter.datalayer.repositories.MenuForUserRepository;
import com.safaricom.dxl.sms.transmitter.model.pojo.Menu;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MenuForUserAoImplTest {

    @Mock
    private MenuForUserRepository menuForUserRepository;

    @InjectMocks
    private MenuForUserAoImpl menuForUserAo;

    @Captor
    private ArgumentCaptor<MenuForUser> menuForUserCaptor;

    private MenuForUser menuForUser;
    private final String userId = "254722000000";
    private final String shortCode = "12345";

    @BeforeEach
    void setUp() {
        List<Menu> menuList = new ArrayList<>();
        menuList.add(Menu.builder().holder("1").value("Menu 1").build());
        menuList.add(Menu.builder().holder("2").value("Menu 2").build());

        menuForUser = MenuForUser.builder()
                .id("test-id")
                .userId(userId)
                .shortCode(shortCode)
                .available(true)
                .menu(menuList)
                .created(LocalDateTime.now())
                .lastUpdated(LocalDateTime.now())
                .build();
    }

    @Nested
    class AddTests {

        @Test
        void add_withValidInput_shouldReturnSavedMenu() {
            // Arrange
            when(menuForUserRepository.save(any(MenuForUser.class))).thenReturn(Mono.just(menuForUser));

            // Act & Assert
            StepVerifier.create(menuForUserAo.add(menuForUser))
                    .expectNext(menuForUser)
                    .verifyComplete();
        }

        @Test
        void add_withError_shouldPropagateError() {
            // Arrange
            when(menuForUserRepository.save(any(MenuForUser.class)))
                    .thenReturn(Mono.error(new RuntimeException("Database error")));

            // Act & Assert
            StepVerifier.create(menuForUserAo.add(menuForUser))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Database error"))
                    .verify();
        }
    }

    @Nested
    class UpdateTests {

        @Test
        void update_withValidInput_shouldUpdateLastUpdatedAndReturnSavedMenu() {
            // Arrange
            when(menuForUserRepository.save(any(MenuForUser.class))).thenReturn(Mono.just(menuForUser));

            // Act
            Mono<MenuForUser> result = menuForUserAo.update(menuForUser);

            // Assert
            StepVerifier.create(result)
                    .expectNext(menuForUser)
                    .verifyComplete();

            verify(menuForUserRepository).save(menuForUserCaptor.capture());
            MenuForUser capturedMenu = menuForUserCaptor.getValue();
            assertNotNull(capturedMenu.getLastUpdated());
            assertTrue(capturedMenu.getLastUpdated().isAfter(menuForUser.getCreated()));
        }

        @Test
        void update_withError_shouldPropagateError() {
            // Arrange
            when(menuForUserRepository.save(any(MenuForUser.class)))
                    .thenReturn(Mono.error(new RuntimeException("Database error")));

            // Act & Assert
            StepVerifier.create(menuForUserAo.update(menuForUser))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Database error"))
                    .verify();
        }
    }

    @Nested
    class GetMenuByUserIdTests {

        @Test
        void getMenuByUserId_withValidInput_shouldReturnMenu() {
            // Arrange
            when(menuForUserRepository.findMenuForUserByUserId(userId)).thenReturn(Mono.just(menuForUser));

            // Act & Assert
            StepVerifier.create(menuForUserAo.getMenuByUserId(userId))
                    .expectNext(menuForUser)
                    .verifyComplete();
        }

        @Test
        void getMenuByUserId_withNonExistingMenu_shouldReturnEmpty() {
            // Arrange
            when(menuForUserRepository.findMenuForUserByUserId(userId)).thenReturn(Mono.empty());

            // Act & Assert
            StepVerifier.create(menuForUserAo.getMenuByUserId(userId))
                    .verifyComplete();
        }

        @Test
        void getMenuByUserId_withError_shouldPropagateError() {
            // Arrange
            when(menuForUserRepository.findMenuForUserByUserId(userId))
                    .thenReturn(Mono.error(new RuntimeException("Database error")));

            // Act & Assert
            StepVerifier.create(menuForUserAo.getMenuByUserId(userId))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Database error"))
                    .verify();
        }
    }

    @Nested
    class GetMenuByUserIdAndShortCodeTests {

        @Test
        void getMenuByUserIdAndShortCode_withValidInput_shouldReturnMenu() {
            // Arrange
            when(menuForUserRepository.findMenuForUserByUserIdAndShortCode(userId, shortCode))
                    .thenReturn(Mono.just(menuForUser));

            // Act & Assert
            StepVerifier.create(menuForUserAo.getMenuByUserIdAndShortCode(userId, shortCode))
                    .expectNext(menuForUser)
                    .verifyComplete();
        }

        @Test
        void getMenuByUserIdAndShortCode_withNonExistingMenu_shouldReturnEmpty() {
            // Arrange
            when(menuForUserRepository.findMenuForUserByUserIdAndShortCode(userId, shortCode))
                    .thenReturn(Mono.empty());

            // Act & Assert
            StepVerifier.create(menuForUserAo.getMenuByUserIdAndShortCode(userId, shortCode))
                    .verifyComplete();
        }

        @Test
        void getMenuByUserIdAndShortCode_withError_shouldPropagateError() {
            // Arrange
            when(menuForUserRepository.findMenuForUserByUserIdAndShortCode(userId, shortCode))
                    .thenReturn(Mono.error(new RuntimeException("Database error")));

            // Act & Assert
            StepVerifier.create(menuForUserAo.getMenuByUserIdAndShortCode(userId, shortCode))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Database error"))
                    .verify();
        }
    }
}
