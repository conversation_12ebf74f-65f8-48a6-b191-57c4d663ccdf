package com.safaricom.dxl.sms.transmitter.entry.controller;

import com.safaricom.dxl.sms.transmitter.model.dto.SmsRequest;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.service.SmsRequestRouter;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SendSmsControllerV1Test {

    @Mock
    private SmsRequestRouter smsRequestRouter;

    @InjectMocks
    private SendSmsControllerV1 controller;

    private SmsRequest smsRequest;
    private Map<String, String> headers;
    private String authToken;
    private WsResponse successResponse;

    @BeforeEach
    void setUp() {
        // Create a valid SMS request
        smsRequest = new SmsRequest();
        smsRequest.setCorrelationId("test-correlation-id");
        smsRequest.setShortCode("12345");
        smsRequest.setCustomerPhoneNumber("254712345678");
        
        Message message = new Message();
        message.setType("text");
        message.setDetails("Test message");
        smsRequest.setMessage(message);

        // Create headers
        headers = new HashMap<>();
        headers.put("X-Conversation-ID", "test-conversation-id");
        headers.put("X-Source-System", "test-source-system");

        // Set auth token
        authToken = "test-auth-token";

        // Create success response
        successResponse = Stub.mockWsResponse("202", "Request accepted", null);

        // Mock the router
        when(smsRequestRouter.handleApiRequest(any(SmsRequest.class), anyString(), any(LocalDateTime.class), anyMap()))
                .thenReturn(Mono.just(successResponse));
    }

    @Test
    void sendSms_shouldReturnSuccessResponse() {
        // Act & Assert
        StepVerifier.create(controller.sendSms(headers, authToken, smsRequest))
                .expectNext(successResponse)
                .verifyComplete();
    }
}
