package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.service.StreamingService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_INTEGRATION_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_SUCCESS;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DxlApiRespProcessorTest {

    @Mock
    private MsConfigProperties properties;

    @Mock
    private StreamingService streamingService;

    @Mock
    private WsResponseMapper responseMapper;

    @InjectMocks
    private DxlApiRespProcessor apiRespProcessor;

    private WsResponse response;
    private String sourceAddress;
    private String destinationAddress;
    private String endPoint;
    private String message;
    private LocalDateTime reqReceiveTime;
    private Map<String, String> headers;
    private WsResponse successResponse;
    private WsResponse errorResponse;

    @BeforeEach
    void setUp() {
        // Set up test data
        sourceAddress = "12345";
        destinationAddress = "254722000000";
        endPoint = "https://test-endpoint.com";
        message = "Test message";
        reqReceiveTime = LocalDateTime.now();
        headers = new HashMap<>();
        headers.put(X_CONVERSATION_ID, "test-conversation-id");

        // Create responses
        response = Stub.mockWsResponse("200", "Success", null);
        successResponse = Stub.mockWsResponse("200", "Success", null);
        errorResponse = Stub.mockWsResponse("500", "Error", null);

        // Mock properties
        when(properties.getDxlEndpoint()).thenReturn("https://dxl-endpoint.com");

        // Mock streaming service
        when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.just(successResponse));

        // Mock response mapper
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), eq(NULL), anyString(), anyString(), eq(FALSE), anyMap()))
                .thenReturn(Mono.just(successResponse));
        when(responseMapper.setApiResponse(eq(ERR_INTEGRATION_ERROR), eq(NULL), anyString(), anyString(), eq(FALSE), anyMap()))
                .thenReturn(Mono.just(errorResponse));
    }

    @Nested
    class ProcessApiResponseTests {

        @Test
        void processApiResponse_withSuccessResponseCode_shouldReturnSuccessResponse() {
            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endPoint, message, reqReceiveTime, headers))
                    .expectNext(successResponse)
                    .verifyComplete();
        }

        @Test
        void processApiResponse_withErrorResponseCode_shouldReturnErrorResponse() {
            // Arrange
            response = Stub.mockWsResponse("400", "Bad Request", null);

            // Act & Assert
            StepVerifier.create(apiRespProcessor.processApiResponse(response, sourceAddress, destinationAddress, endPoint, message, reqReceiveTime, headers))
                    .expectNext(errorResponse)
                    .verifyComplete();
        }
    }
}
