package com.safaricom.dxl.sms.transmitter;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test class for the main application.
 * This is a simple test to ensure that the main class can be instantiated.
 */
class MsSmsTransmitterApplicationTest {

    /**
     * Test that the application class can be instantiated.
     * This is a simple test to ensure that the main class can be created.
     */
    @Test
    void testApplicationCanBeInstantiated() {
        // Create an instance of the application class
        MsSmsTransmitterApplication application = new MsSmsTransmitterApplication();
        // Assert that the application instance is not null
        assertNotNull(application, "Application instance should not be null");
    }

    /**
     * Test that the main method exists.
     * This is a simple test to ensure that the main method exists.
     */
    @Test
    void testMainMethodExists() {
        // This test simply verifies that the main method exists
        // We don't actually call it to avoid starting the application
        try {
            var mainMethod = MsSmsTransmitterApplication.class.getMethod("main", String[].class);
            // Assert that the main method exists
            assertNotNull(mainMethod, "Main method should exist");
            // Assert that the main method has the correct modifiers (public static)
            assertTrue(java.lang.reflect.Modifier.isPublic(mainMethod.getModifiers()), "Main method should be public");
            assertTrue(java.lang.reflect.Modifier.isStatic(mainMethod.getModifiers()), "Main method should be static");
        } catch (NoSuchMethodException e) {
            throw new AssertionError("main method not found", e);
        }
    }
}
