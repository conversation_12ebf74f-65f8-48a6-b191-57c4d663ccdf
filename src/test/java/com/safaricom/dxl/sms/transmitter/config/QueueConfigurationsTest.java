package com.safaricom.dxl.sms.transmitter.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistrar;
import org.springframework.amqp.support.converter.DefaultClassMapper;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.handler.annotation.support.DefaultMessageHandlerMethodFactory;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class QueueConfigurationsTest {

    @Mock
    private MsConfigProperties properties;

    @Mock
    private ConnectionFactory connectionFactory;

    @Mock
    private RabbitListenerEndpointRegistrar registrar;

    @InjectMocks
    private QueueConfigurations queueConfigurations;

    private static final String EXCHANGE_NAME = "test-exchange";
    private static final String QUEUE_NAME = "test-queue";

    // We'll use real objects instead of mocks for most of these tests
    // This avoids unnecessary stubbing errors

    @Test
    void exchange_shouldCreateDirectExchange() {
        // Arrange
        when(properties.getBotExchangeName()).thenReturn(EXCHANGE_NAME);

        // Act
        DirectExchange exchange = queueConfigurations.exchange();

        // Assert
        assertNotNull(exchange);
        assertEquals(EXCHANGE_NAME, exchange.getName());
        assertTrue(exchange.isDurable());
        assertTrue(exchange.isAutoDelete());
    }

    @Test
    void smsRequestQueue_shouldCreateQueue() {
        // Arrange
        when(properties.getSmsRequestQueue()).thenReturn(QUEUE_NAME);

        // Act
        Queue queue = queueConfigurations.smsRequestQueue();

        // Assert
        assertNotNull(queue);
        assertEquals(QUEUE_NAME, queue.getName());
        assertTrue(queue.isDurable());
    }

    @Test
    void bindingForRequestsQueueSmsQueue_shouldCreateBinding() {
        // Arrange
        when(properties.getSmsRequestQueue()).thenReturn(QUEUE_NAME);
        when(properties.getBotExchangeName()).thenReturn(EXCHANGE_NAME);

        Queue queue = queueConfigurations.smsRequestQueue();
        DirectExchange exchange = queueConfigurations.exchange();

        // Act
        Binding binding = queueConfigurations.bindingForRequestsQueueSmsQueue(queue, exchange);

        // Assert
        assertNotNull(binding);
        assertEquals(QUEUE_NAME, binding.getRoutingKey());
    }

    @Test
    void rabbitTemplate_shouldCreateRabbitTemplate() {
        // Act
        RabbitTemplate rabbitTemplate = queueConfigurations.rabbitTemplate(connectionFactory);

        // Assert
        assertNotNull(rabbitTemplate);
    }

    @Test
    void producerJackson2MessageConverter_shouldCreateConverter() {
        // Act
        Jackson2JsonMessageConverter converter = queueConfigurations.producerJackson2MessageConverter();

        // Assert
        assertNotNull(converter);
    }

    @Test
    void consumerJackson2MessageConverter_shouldCreateConverter() {
        // Act
        MappingJackson2MessageConverter converter = queueConfigurations.consumerJackson2MessageConverter();

        // Assert
        assertNotNull(converter);
    }

    @Test
    void messageHandlerMethodFactory_shouldCreateFactory() {
        // Act
        DefaultMessageHandlerMethodFactory factory = queueConfigurations.messageHandlerMethodFactory();

        // Assert
        assertNotNull(factory);
    }

    @Test
    void classMapper_shouldCreateMapper() {
        // Act
        DefaultClassMapper mapper = queueConfigurations.classMapper();

        // Assert
        assertNotNull(mapper);
    }

    @Test
    void configureRabbitListeners_shouldConfigureRegistrar() {
        // Act
        queueConfigurations.configureRabbitListeners(registrar);

        // Assert - we can't easily verify the method call with the exact same factory instance
        // So we'll just verify that the method was called with any factory
        verify(registrar).setMessageHandlerMethodFactory(any(DefaultMessageHandlerMethodFactory.class));
    }
}
