package com.safaricom.dxl.sms.transmitter.service.processor.impl;

import com.safaricom.dxl.sms.transmitter.service.integration.impl.TibcoIntegrationProcessor;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TibcoIntegrationShortCodeProcessorTest {

    @Mock
    private Utilities utilities;

    @Mock
    private TibcoIntegrationProcessor tibcoIntegrationProcessor;

    @InjectMocks
    private TibcoIntegrationShortCodeProcessor processor;

    private String sourceAddress;
    private String destinationAddress;
    private String message;
    private LocalDateTime reqReceiveTime;
    private Map<String, String> headers;
    private WsResponse successResponse;

    @BeforeEach
    void setUp() {
        sourceAddress = "12345";
        destinationAddress = "254712345678";
        message = "Test message";
        reqReceiveTime = LocalDateTime.now();
        headers = new HashMap<>();
        headers.put("X-Conversation-ID", "test-conversation-id");
        headers.put("X-Source-System", "test-source-system");

        successResponse = Stub.mockWsResponse("202", "Request accepted", null);
    }

    @Test
    void supports_whenShortCodeMappedToTibco_shouldReturnTrue() {
        // Arrange
        when(utilities.isShortCodeMappedToTibco(sourceAddress)).thenReturn(true);

        // Act & Assert
        assertTrue(processor.supports(sourceAddress));
    }

    @Test
    void supports_whenShortCodeNotMappedToTibco_shouldReturnFalse() {
        // Arrange
        when(utilities.isShortCodeMappedToTibco(sourceAddress)).thenReturn(false);

        // Act & Assert
        assertFalse(processor.supports(sourceAddress));
    }

    @Test
    void process_shouldDelegateToTibcoIntegrationProcessor() {
        // Arrange
        when(tibcoIntegrationProcessor.processIntegrationRequest(
                anyString(), anyString(), anyString(), any(LocalDateTime.class), anyMap()))
                .thenReturn(Mono.just(successResponse));

        // Act & Assert
        StepVerifier.create(processor.process(sourceAddress, destinationAddress, message, reqReceiveTime, headers))
                .expectNext(successResponse)
                .verifyComplete();

        // Verify that the integration processor was called with the correct parameters
        verify(tibcoIntegrationProcessor).processIntegrationRequest(
                sourceAddress, destinationAddress, message, reqReceiveTime, headers);
    }
}
