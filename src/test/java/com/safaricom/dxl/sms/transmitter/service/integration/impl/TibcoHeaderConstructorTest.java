package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_SOURCE_SYSTEM;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class TibcoHeaderConstructorTest {

    @InjectMocks
    private TibcoHeaderConstructor headerConstructor;

    private String destinationAddress;
    private Map<String, String> headers;

    @BeforeEach
    void setUp() {
        destinationAddress = "254722000000";
        headers = new HashMap<>();
        headers.put(X_CONVERSATION_ID, "test-conversation-id");
    }

    @Test
    void constructHeaders_shouldCreateCorrectHeaders() {
        // Act
        Mono<Consumer<HttpHeaders>> result = headerConstructor.constructHeaders(destinationAddress, headers);

        // Assert
        StepVerifier.create(result)
                .expectNextMatches(consumer -> {
                    HttpHeaders httpHeaders = new HttpHeaders();
                    consumer.accept(httpHeaders);
                    
                    assertEquals(MediaType.APPLICATION_JSON, httpHeaders.getContentType());
                    assertEquals(MediaType.APPLICATION_JSON_VALUE, httpHeaders.getFirst(HttpHeaders.ACCEPT_ENCODING));
                    assertEquals(MediaType.APPLICATION_JSON_VALUE, httpHeaders.getFirst(HttpHeaders.ACCEPT));
                    assertEquals("Basic ZWFpX3Ntc19zZW5kZXI6ZWExQFNNU3NlbmRlciE=", httpHeaders.getFirst(HttpHeaders.AUTHORIZATION));
                    assertEquals("zuri", httpHeaders.getFirst(X_SOURCE_SYSTEM));
                    assertEquals("test-conversation-id", httpHeaders.getFirst(X_CONVERSATION_ID));
                    
                    return true;
                })
                .verifyComplete();
    }
}
