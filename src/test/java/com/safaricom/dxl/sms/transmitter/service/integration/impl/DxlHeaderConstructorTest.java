package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class DxlHeaderConstructorTest {

    @InjectMocks
    private DxlHeaderConstructor headerConstructor;
    @Mock
    private MsConfigProperties properties;

    private String destinationAddress;
    private Map<String, String> headers;
    private String auth;

    @BeforeEach
    void setUp() {
        destinationAddress = "254722000000";
        auth = "Basic ZGV2ZWxvcG1lbnQ6a3MlcnJSK1NKNSZDWFAzag==";
        headers = Stub.mockZuriHeaders();
        headers.put(X_CONVERSATION_ID, "test-conversation-id");
    }

    @Test
    void constructHeaders_shouldCreateCorrectHeaders() {
        // Act
        Mono<Consumer<HttpHeaders>> result = headerConstructor.constructHeaders(destinationAddress, headers);

        // Assert
        StepVerifier.create(result)
                .expectNextMatches(consumer -> {
                    HttpHeaders httpHeaders = new HttpHeaders();
                    consumer.accept(httpHeaders);
                    
                    assertEquals(MediaType.APPLICATION_JSON, httpHeaders.getContentType());
                    assertEquals(MediaType.APPLICATION_JSON_VALUE, httpHeaders.getFirst(HttpHeaders.ACCEPT_ENCODING));
                    assertEquals(MediaType.APPLICATION_JSON_VALUE, httpHeaders.getFirst(HttpHeaders.ACCEPT));
                    assertEquals(auth, httpHeaders.getFirst(HttpHeaders.AUTHORIZATION));
                    assertEquals("zuri", httpHeaders.getFirst(X_SOURCE_SYSTEM));
                    assertEquals("test-conversation-id", httpHeaders.getFirst(X_CONVERSATION_ID));
                    assertEquals(destinationAddress, httpHeaders.getFirst(X_MSISDN));

                    return true;
                })
                .verifyComplete();
    }
}
