package com.safaricom.dxl.sms.transmitter.service.impl;

import com.safaricom.dxl.sms.transmitter.exception.BadRequestException;
import com.safaricom.dxl.sms.transmitter.exception.InternalServerErrorException;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.model.pojo.MessageFormat;
import com.safaricom.dxl.sms.transmitter.service.MessageConversionService;
import com.safaricom.dxl.sms.transmitter.service.SmsMessageProcessor;
import com.safaricom.dxl.sms.transmitter.service.processor.IApiProcessor;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.NULL;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SmsMessageProcessorTest {

    @Mock
    private WsResponseMapper responseMapper;

    @Mock
    private Utilities utilities;

    @Mock
    private MessageConversionService messageConversionService;

    @Mock
    private IApiProcessor apiProcessor;

    @InjectMocks
    private SmsMessageProcessor smsMessageProcessor;

    private Map<String, String> headers;
    private LocalDateTime requestTime;
    private String shortCode;
    private String phoneNumber;
    private Message message;
    private List<IApiProcessor> apiProcessors;

    @BeforeEach
    void setUp() {
        headers = Stub.mockHeaders();
        requestTime = Stub.mockDateTime();
        shortCode = "12345";
        phoneNumber = "254722000000";
        message = Stub.mockTextMessage();

        apiProcessors = new ArrayList<>();
        apiProcessors.add(apiProcessor);

        smsMessageProcessor = new SmsMessageProcessor(responseMapper, utilities, apiProcessors, messageConversionService);

        Mockito.lenient().when(responseMapper.setApiResponse(anyString(), anyString(), anyString(), anyBoolean(), any()))
                .thenReturn(Mono.just(Stub.mockWsResponse("200", "Success", null)));

        Mockito.lenient().when(utilities.calculateProcessingTime(any())).thenReturn("100ms");
    }

    @Nested
    class ProcessSmsMessageTests {

        @Test
        void processSmsMessage_withValidTextMessage_shouldSucceed() {
            // Arrange
            MessageFormat messageFormat = Stub.mockMessageFormat();
            when(messageConversionService.prepareMessageFormat(any(), any(), any())).thenReturn(messageFormat);
            when(messageConversionService.convertMenuToTextFormat(anyString(), anyString(), any(), anyString(), any(), any()))
                    .thenReturn(Mono.just("Converted message"));
            when(messageConversionService.decodeSpecialCharacters(anyString())).thenReturn(Mono.just("Decoded message"));
            when(apiProcessor.supports(anyString())).thenReturn(true);

            WsResponse successResponse = Stub.mockWsResponse("200", "Success", null);
            when(apiProcessor.process(anyString(), anyString(), anyString(), any(), any()))
                    .thenReturn(Mono.just(successResponse));

            // Act & Assert
            try {
                WsResponse response = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();
                assertNotNull(response);
                assertEquals(200, response.getHeader().getResponseCode());
                assertEquals(response.getHeader().getResponseMessage(), response.getHeader().getResponseMessage());
            } catch (Exception e) {
                fail("Should not throw exception: " + e.getMessage());
            }
        }

        @Test
        void processSmsMessage_withNullShortCode_shouldReturnError() {
            // Act
            Mono<WsResponse> result = smsMessageProcessor.processSmsMessage(null, phoneNumber, message, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof BadRequestException &&
                    "Short code cannot be null or empty".equals(error.getMessage()))
                .verify();
        }

        @Test
        void processSmsMessage_withEmptyShortCode_shouldReturnError() {
            // Act
            Mono<WsResponse> result = smsMessageProcessor.processSmsMessage("", phoneNumber, message, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof BadRequestException &&
                    "Short code cannot be null or empty".equals(error.getMessage()))
                .verify();
        }

        @Test
        void processSmsMessage_withNullPhoneNumber_shouldReturnError() {
            // Act
            Mono<WsResponse> result = smsMessageProcessor.processSmsMessage(shortCode, null, message, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof BadRequestException &&
                    "Customer phone number cannot be null or empty".equals(error.getMessage()))
                .verify();
        }

        @Test
        void processSmsMessage_withEmptyPhoneNumber_shouldReturnError() {
            // Act
            Mono<WsResponse> result = smsMessageProcessor.processSmsMessage(shortCode, "", message, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof BadRequestException &&
                    "Customer phone number cannot be null or empty".equals(error.getMessage()))
                .verify();
        }

        @Test
        void processSmsMessage_withNullMessage_shouldReturnError() {
            // Act
            Mono<WsResponse> result = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, null, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof BadRequestException &&
                    "Message payload cannot be null".equals(error.getMessage()))
                .verify();
        }

        @Test
        void processSmsMessage_withNullMessageType_shouldReturnError() {
            // Arrange
            Message messageWithNullType = Stub.mockTextMessage();
            messageWithNullType.setType(null);

            // Act
            Mono<WsResponse> result = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, messageWithNullType, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof BadRequestException &&
                    "Message type cannot be null".equals(error.getMessage()))
                .verify();
        }

        @Test
        void processSmsMessage_whenPrepareMessageFormatThrowsBadRequestException_shouldPropagateError() {
            // Arrange
            BadRequestException exception = new BadRequestException("Invalid message format", ERR_BAD_REQUEST);
            when(messageConversionService.prepareMessageFormat(any(), any(), any()))
                    .thenThrow(exception);

            // Act & Assert
            try {
                smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();
                fail("Expected exception to be thrown");
            } catch (Exception e) {
                // Just verify that an exception was thrown
                assertTrue(true, "Exception was thrown as expected");
            }
        }

        @Test
        void processSmsMessage_whenPrepareMessageFormatThrowsInternalServerErrorException_shouldHandleGracefully() {
            // Arrange
            when(messageConversionService.prepareMessageFormat(any(), any(), any()))
                    .thenThrow(new InternalServerErrorException("Server error", ERR_SERVER_ERROR));
            when(apiProcessor.supports(anyString())).thenReturn(true);

            WsResponse successResponse = Stub.mockWsResponse("200", "Success", null);
            when(apiProcessor.process(anyString(), anyString(), eq(DEFAULT_ERR_RESP), any(), any()))
                    .thenReturn(Mono.just(successResponse));

            // Act & Assert
            try {
                WsResponse response = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();
                assertNotNull(response);
                assertEquals(200, response.getHeader().getResponseCode());
                assertEquals(response.getHeader().getResponseMessage(), response.getHeader().getResponseMessage());
            } catch (Exception e) {
                fail("Should not throw exception: " + e.getMessage());
            }
        }

        @Test
        void processSmsMessage_whenConvertMenuToTextFormatFails_shouldHandleGracefully() {
            // Arrange
            MessageFormat messageFormat = Stub.mockMessageFormat();
            when(messageConversionService.prepareMessageFormat(any(), any(), any())).thenReturn(messageFormat);
            when(messageConversionService.convertMenuToTextFormat(anyString(), anyString(), any(), anyString(), any(), any()))
                    .thenReturn(Mono.error(new RuntimeException("Conversion error")));
            when(apiProcessor.supports(anyString())).thenReturn(true);

            WsResponse successResponse = Stub.mockWsResponse("200", "Success", null);
            when(apiProcessor.process(anyString(), anyString(), eq(DEFAULT_ERR_RESP), any(), any()))
                    .thenReturn(Mono.just(successResponse));

            // Act & Assert
            try {
                WsResponse response = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();
                assertNotNull(response);
                assertEquals(200, response.getHeader().getResponseCode());
                assertEquals(response.getHeader().getResponseMessage(), response.getHeader().getResponseMessage());
            } catch (Exception e) {
                fail("Should not throw exception: " + e.getMessage());
            }
        }

        @Test
        void processSmsMessage_whenInternalServerErrorExceptionOccurs_shouldHandleGracefully() {
            // Arrange
            MessageFormat messageFormat = Stub.mockMessageFormat();
            when(messageConversionService.prepareMessageFormat(any(), any(), any())).thenReturn(messageFormat);
            when(messageConversionService.convertMenuToTextFormat(anyString(), anyString(), any(), anyString(), any(), any()))
                .thenReturn(Mono.error(new InternalServerErrorException("Database error", "500")));

            // Mock the handleErrorGracefully behavior
            when(apiProcessor.supports(anyString())).thenReturn(true);
            when(apiProcessor.process(anyString(), anyString(), eq(DEFAULT_ERR_RESP), any(), any()))
                .thenReturn(Mono.just(Stub.mockWsResponse("200", "Success", null)));

            // Act
            WsResponse result = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();

            // Assert
            assertNotNull(result);
            assertEquals(200, result.getHeader().getResponseCode());

            // Verify that the default error response was used
            verify(apiProcessor).process(shortCode, phoneNumber, DEFAULT_ERR_RESP, requestTime, headers);
        }

        @Test
        void processSmsMessage_whenDecodeSpecialCharactersFails_shouldHandleGracefully() {
            // Arrange
            MessageFormat messageFormat = Stub.mockMessageFormat();
            when(messageConversionService.prepareMessageFormat(any(), any(), any())).thenReturn(messageFormat);
            when(messageConversionService.convertMenuToTextFormat(anyString(), anyString(), any(), anyString(), any(), any()))
                    .thenReturn(Mono.just("Converted message"));
            when(messageConversionService.decodeSpecialCharacters(anyString()))
                    .thenReturn(Mono.error(new RuntimeException("Decoding error")));
            when(apiProcessor.supports(anyString())).thenReturn(true);

            WsResponse successResponse = Stub.mockWsResponse("200", "Success", null);
            when(apiProcessor.process(anyString(), anyString(), eq(DEFAULT_ERR_RESP), any(), any()))
                    .thenReturn(Mono.just(successResponse));

            // Act & Assert
            try {
                WsResponse response = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();
                assertNotNull(response);
                assertEquals(200, response.getHeader().getResponseCode());
                assertEquals(response.getHeader().getResponseMessage(), response.getHeader().getResponseMessage());
            } catch (Exception e) {
                fail("Should not throw exception: " + e.getMessage());
            }
        }

        @Test
        void processSmsMessage_whenUnexpectedExceptionOccurs_shouldHandleGracefully() {
            // Arrange
            when(messageConversionService.prepareMessageFormat(any(), any(), any()))
                    .thenThrow(new RuntimeException("Unexpected error"));
            when(apiProcessor.supports(anyString())).thenReturn(true);

            WsResponse successResponse = Stub.mockWsResponse("200", "Success", null);
            when(apiProcessor.process(anyString(), anyString(), eq(DEFAULT_ERR_RESP), any(), any()))
                    .thenReturn(Mono.just(successResponse));

            // Act & Assert
            try {
                WsResponse response = smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();
                assertNotNull(response);
                assertEquals(200, response.getHeader().getResponseCode());
                assertEquals(response.getHeader().getResponseMessage(), response.getHeader().getResponseMessage());
            } catch (Exception e) {
                fail("Should not throw exception: " + e.getMessage());
            }
        }

        @Test
        void processSmsMessage_whenBadRequestExceptionOccursDuringProcessing_shouldPropagateError() {
            // Arrange
            MessageFormat messageFormat = Stub.mockMessageFormat();
            when(messageConversionService.prepareMessageFormat(any(), any(), any())).thenReturn(messageFormat);
            when(messageConversionService.convertMenuToTextFormat(anyString(), anyString(), any(), anyString(), any(), any()))
                .thenReturn(Mono.error(new BadRequestException("Invalid message format", ERR_BAD_REQUEST)));

            // Act & Assert
            try {
                smsMessageProcessor.processSmsMessage(shortCode, phoneNumber, message, headers, requestTime).block();
                fail("Expected exception to be thrown");
            } catch (Exception e) {
                // Just verify that an exception was thrown
                assertTrue(true, "Exception was thrown as expected");
            }
        }
    }

    @Nested
    class RouteRequestTests {

        @Test
        void routeRequest_whenProcessorSupportsShortCode_shouldRouteToProcessor() {
            // Arrange
            when(apiProcessor.supports(anyString())).thenReturn(true);
            WsResponse successResponse = Stub.mockWsResponse("200", "Success", null);
            when(apiProcessor.process(anyString(), anyString(), anyString(), any(), any()))
                    .thenReturn(Mono.just(successResponse));

            // Act & Assert
            try {
                WsResponse response = smsMessageProcessor.routeRequest(shortCode, phoneNumber, "Test message", requestTime, headers).block();
                assertNotNull(response);
                assertEquals(200, response.getHeader().getResponseCode());
            } catch (Exception e) {
                fail("Should not throw exception: " + e.getMessage());
            }
        }

        @Test
        void routeRequest_whenNoProcessorSupportsShortCode_shouldReturnError() {
            // Arrange
            when(apiProcessor.supports(anyString())).thenReturn(false);
            WsResponse errorResponse = Stub.mockWsResponse("500", "Internal Server Error", null);
            when(responseMapper.setApiResponse(eq(ERR_SERVER_ERROR), eq(NULL), eq(TRANS_ROUTE_REQUEST), eq(FALSE), any()))
                    .thenReturn(Mono.just(errorResponse));

            // Act
            WsResponse result = smsMessageProcessor.routeRequest(shortCode, phoneNumber, "Test message", requestTime, headers).block();

            // Assert
            assertNotNull(result);
            assertEquals(500, result.getHeader().getResponseCode());
            assertEquals("500", result.getHeader().getResponseMessage());

            // Verify that the processor was checked for support
            verify(apiProcessor).supports(shortCode);
            verify(responseMapper).setApiResponse(eq(ERR_SERVER_ERROR), eq(NULL), eq(TRANS_ROUTE_REQUEST), eq(FALSE), any());
        }
    }


}
