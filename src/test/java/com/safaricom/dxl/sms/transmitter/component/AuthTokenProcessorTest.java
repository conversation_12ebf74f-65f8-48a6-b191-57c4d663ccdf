package com.safaricom.dxl.sms.transmitter.component;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.datalayer.dao.ICacheAo;
import com.safaricom.dxl.sms.transmitter.exception.InternalServerErrorException;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.ApacheAuthToken;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_REDIS_GET;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_REDIS_WRITE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_SOURCE_SYSTEM;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AuthTokenProcessorTest {

    @Mock
    private MsConfigProperties properties;

    @Mock
    private ICacheAo cacheAo;

    @Mock
    private HttpRequestHandler httpRequestHandler;

    @Mock
    private WsStarterService wsStarterService;

    @InjectMocks
    private AuthTokenProcessor authTokenProcessor;

    private Map<String, String> headers;
    private ApacheAuthToken apacheAuthToken;
    private String tokenString;

    @BeforeEach
    void setUp() {
        headers = new HashMap<>();
        headers.put(X_CONVERSATION_ID, "test-conversation-id");
        headers.put(X_SOURCE_SYSTEM, "test-source-system");

        apacheAuthToken = Stub.mockApacheAuthToken();
        tokenString = apacheAuthToken.getTokenType() + " " + apacheAuthToken.getAccessToken();

        lenient().when(properties.getApacheAuthTokenKey()).thenReturn("apache-auth-token");
        lenient().when(properties.getApacheAuthEndpoint()).thenReturn("https://test-auth-endpoint.com");
        lenient().when(properties.getApacheAuthAuthorization()).thenReturn("Basic test-auth");
        lenient().when(properties.getApacheAuthRealm()).thenReturn("test-realm");
        lenient().when(properties.getApacheAuthClientId()).thenReturn("test-client-id");
        lenient().when(properties.getApacheAuthUsername()).thenReturn("test-username");
        lenient().when(properties.getApacheAuthPassword()).thenReturn("test-password");
        lenient().when(properties.getAuthTokenExpireTime()).thenReturn(60L);
        lenient().when(wsStarterService.serialize(any())).thenReturn("{\"payload\":\"test\"}");
        lenient().when(httpRequestHandler.post(anyString(), any(), anyString(), eq(ApacheAuthToken.class)))
                .thenReturn(Mono.just(apacheAuthToken));
    }

    @Nested
    class GetAuthTokenTests {

        @Test
        void getAuthToken_whenCachingEnabled_andTokenInCache_shouldReturnCachedToken() {
            // Arrange
            when(properties.isCacheAuthTokenEnabled()).thenReturn(true);
            when(cacheAo.getCache("apache-auth-token", String.class)).thenReturn(Mono.just(tokenString));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.getAuthToken(headers))
                    .expectNext(tokenString)
                    .verifyComplete();

            verify(cacheAo).getCache("apache-auth-token", String.class);
        }

        @Test
        void getAuthToken_whenCachingEnabled_andTokenNotInCache_shouldFetchNewToken() {
            // Arrange
            when(properties.isCacheAuthTokenEnabled()).thenReturn(true);
            when(cacheAo.getCache("apache-auth-token", String.class)).thenReturn(Mono.empty());
            when(httpRequestHandler.post(anyString(), any(), anyString(), eq(ApacheAuthToken.class)))
                    .thenReturn(Mono.just(apacheAuthToken));
            when(cacheAo.setCache(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.getAuthToken(headers))
                    .expectNext(tokenString)
                    .verifyComplete();

            verify(cacheAo).getCache("apache-auth-token", String.class);
            verify(httpRequestHandler).post(anyString(), any(), anyString(), eq(ApacheAuthToken.class));
            verify(cacheAo).setCache(eq("apache-auth-token"), eq(tokenString), any(Duration.class));
        }

        @Test
        void getAuthToken_whenCachingDisabled_shouldFetchNewToken() {
            // Arrange
            when(properties.isCacheAuthTokenEnabled()).thenReturn(false);
            when(httpRequestHandler.post(anyString(), any(), anyString(), eq(ApacheAuthToken.class)))
                    .thenReturn(Mono.just(apacheAuthToken));
            when(cacheAo.setCache(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.getAuthToken(headers))
                    .expectNext(tokenString)
                    .verifyComplete();

            verify(cacheAo, never()).getCache(anyString(), any());
            verify(httpRequestHandler).post(anyString(), any(), anyString(), eq(ApacheAuthToken.class));
            verify(cacheAo).setCache(eq("apache-auth-token"), eq(tokenString), any(Duration.class));
        }

        @Test
        void getAuthToken_whenCacheRetrievalFails_shouldThrowException() {
            // Arrange
            when(properties.isCacheAuthTokenEnabled()).thenReturn(true);
            when(cacheAo.getCache("apache-auth-token", String.class))
                    .thenReturn(Mono.error(new RuntimeException("Cache error")));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.getAuthToken(headers))
                    .expectErrorMatches(throwable ->
                        throwable instanceof InternalServerErrorException &&
                        throwable.getMessage().contains("Cache error") &&
                        ((InternalServerErrorException) throwable).getCode().equals(ERR_REDIS_GET))
                    .verify();

            verify(cacheAo).getCache("apache-auth-token", String.class);
        }
    }

    @Nested
    class FetchNewTokenAndCacheTests {

        @Test
        void fetchNewTokenAndCache_whenSuccessful_shouldReturnToken() {
            // Arrange
            when(httpRequestHandler.post(anyString(), any(), anyString(), eq(ApacheAuthToken.class)))
                    .thenReturn(Mono.just(apacheAuthToken));
            when(cacheAo.setCache(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.fetchNewTokenAndCache(headers))
                    .expectNext(tokenString)
                    .verifyComplete();

            verify(httpRequestHandler).post(anyString(), any(), anyString(), eq(ApacheAuthToken.class));
            verify(cacheAo).setCache(eq("apache-auth-token"), eq(tokenString), any(Duration.class));
        }

        @Test
        void fetchNewTokenAndCache_whenTokenFetchFails_shouldThrowException() {
            // Arrange
            when(httpRequestHandler.post(anyString(), any(), anyString(), eq(ApacheAuthToken.class)))
                    .thenReturn(Mono.error(new RuntimeException("Token fetch error")));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.fetchNewTokenAndCache(headers))
                    .expectErrorMatches(throwable ->
                        throwable instanceof RuntimeException &&
                        throwable.getMessage().contains("Token fetch error"))
                    .verify();

            verify(httpRequestHandler).post(anyString(), any(), anyString(), eq(ApacheAuthToken.class));
            verify(cacheAo, never()).setCache(anyString(), anyString(), any(Duration.class));
        }

        @Test
        void fetchNewTokenAndCache_whenCachingFails_shouldThrowException() {
            // Arrange
            when(httpRequestHandler.post(anyString(), any(), anyString(), eq(ApacheAuthToken.class)))
                    .thenReturn(Mono.just(apacheAuthToken));
            when(cacheAo.setCache(anyString(), anyString(), any(Duration.class)))
                    .thenReturn(Mono.error(new RuntimeException("Cache write error")));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.fetchNewTokenAndCache(headers))
                    .expectErrorMatches(throwable ->
                        throwable instanceof InternalServerErrorException &&
                        throwable.getMessage().contains("Cache write error") &&
                        ((InternalServerErrorException) throwable).getCode().equals(ERR_REDIS_WRITE))
                    .verify();

            verify(httpRequestHandler).post(anyString(), any(), anyString(), eq(ApacheAuthToken.class));
            verify(cacheAo).setCache(eq("apache-auth-token"), eq(tokenString), any(Duration.class));
        }

        @Test
        void fetchNewTokenAndCache_whenCachingSucceedsWithFalse_shouldStillReturnToken() {
            // Arrange
            when(httpRequestHandler.post(anyString(), any(), anyString(), eq(ApacheAuthToken.class)))
                    .thenReturn(Mono.just(apacheAuthToken));
            when(cacheAo.setCache(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(false));

            // Act & Assert
            StepVerifier.create(authTokenProcessor.fetchNewTokenAndCache(headers))
                    .expectNext(tokenString)
                    .verifyComplete();

            verify(httpRequestHandler).post(anyString(), any(), anyString(), eq(ApacheAuthToken.class));
            verify(cacheAo).setCache(eq("apache-auth-token"), eq(tokenString), any(Duration.class));
        }
    }

    @Nested
    class ExtractAuthTokenTests {

        @Test
        void extractAuthToken_whenResponseIsNotApacheAuthToken_shouldThrowException() {
            // Arrange
            Object invalidResponse = "Invalid response";

            // Use reflection to access the private method
            java.lang.reflect.Method extractAuthTokenMethod;
            try {
                extractAuthTokenMethod = AuthTokenProcessor.class.getDeclaredMethod("extractAuthToken", Object.class);
                extractAuthTokenMethod.setAccessible(true);

                // Act & Assert
                Mono<String> result = (Mono<String>) extractAuthTokenMethod.invoke(authTokenProcessor, invalidResponse);

                StepVerifier.create(result)
                        .expectErrorMatches(throwable ->
                            throwable instanceof InternalServerErrorException &&
                            throwable.getMessage().contains("Token Retrieve Error"))
                        .verify();

            } catch (Exception e) {
                fail("Failed to invoke extractAuthToken method: " + e.getMessage());
            }
        }
    }


}
