package com.safaricom.dxl.sms.transmitter.component;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.ApacheAuthToken;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.util.function.Consumer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class HttpRequestHandlerTest {

    @Mock
    private WebClient webClient;

    @Mock
    private MsConfigProperties properties;

    @Mock
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.RequestBodySpec requestBodySpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @InjectMocks
    private HttpRequestHandler httpRequestHandler;

    private String url;
    private Consumer<HttpHeaders> headers;
    private String requestBody;
    private ApacheAuthToken responseBody;

    @BeforeEach
    void setUp() {
        url = "https://test-api.com";
        headers = httpHeaders -> {
            httpHeaders.add("Content-Type", "application/json");
            httpHeaders.add("Authorization", "Bearer test-token");
        };
        requestBody = "{\"key\":\"value\"}";
        responseBody = Stub.mockApacheAuthToken();

        // Mock WebClient chain
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.headers(any())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(any(Class.class))).thenReturn(Mono.just(responseBody));

        // Mock properties
        when(properties.getHttpRetries()).thenReturn((short) 3);
    }

    @Nested
    class PostTests {

        @Test
        void post_withValidRequest_shouldReturnResponse() {
            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectNext(responseBody)
                    .verifyComplete();

            // Verify WebClient chain was called correctly
            verify(webClient).post();
            verify(requestBodyUriSpec).uri(url);
            verify(requestBodySpec).headers(headers);
            verify(requestBodySpec).body(any());
            verify(requestHeadersSpec).retrieve();
            verify(responseSpec).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void post_withErrorResponse_shouldRetryAndPropagateError() {
            // Arrange
            WebClientResponseException exception = new WebClientResponseException(
                    500, "Internal Server Error", null, null, null);

            when(responseSpec.bodyToMono(any(Class.class)))
                    .thenReturn(Mono.error(exception));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectError()
                    .verify();

            // Verify WebClient chain was called correctly
            verify(webClient, atLeast(1)).post();
            verify(requestBodyUriSpec, atLeast(1)).uri(url);
            verify(requestBodySpec, atLeast(1)).headers(headers);
            verify(requestBodySpec, atLeast(1)).body(any());
            verify(requestHeadersSpec, atLeast(1)).retrieve();
            verify(responseSpec, atLeast(1)).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void post_withNetworkError_shouldRetryAndEventuallyFail() {
            // Arrange
            when(responseSpec.bodyToMono(any(Class.class)))
                    .thenReturn(Mono.error(new RuntimeException("Network error")));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectError()
                    .verify();

            // Verify WebClient chain was called correctly
            verify(webClient, atLeast(1)).post();
            verify(requestBodyUriSpec, atLeast(1)).uri(url);
            verify(requestBodySpec, atLeast(1)).headers(headers);
            verify(requestBodySpec, atLeast(1)).body(any());
            verify(requestHeadersSpec, atLeast(1)).retrieve();
            verify(responseSpec, atLeast(1)).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void post_withCustomResponseType_shouldReturnCorrectType() {
            // Arrange
            class CustomResponse {
                private String value;

                public String getValue() { return value; }
                public void setValue(String value) { this.value = value; }
            }

            CustomResponse customResponse = new CustomResponse();
            customResponse.setValue("custom value");

            when(responseSpec.bodyToMono(CustomResponse.class)).thenReturn(Mono.just(customResponse));

            // Act
            Mono<CustomResponse> result = httpRequestHandler.post(url, headers, requestBody, CustomResponse.class);

            // Assert
            StepVerifier.create(result)
                    .expectNext(customResponse)
                    .verifyComplete();

            // Verify WebClient chain was called correctly
            verify(webClient).post();
            verify(requestBodyUriSpec).uri(url);
            verify(requestBodySpec).headers(headers);
            verify(requestBodySpec).body(any());
            verify(requestHeadersSpec).retrieve();
            verify(responseSpec).bodyToMono(CustomResponse.class);
        }
    }

    @Nested
    class RetryLogicTests {

        @Test
        void shouldSucceedImmediately_whenNoErrorOccurs() {
            // Arrange
            reset(responseSpec);
            when(responseSpec.bodyToMono(ApacheAuthToken.class))
                    .thenReturn(Mono.just(responseBody));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectNext(responseBody)
                    .verifyComplete();

            // Verify that the request was attempted only once (no retries needed)
            verify(responseSpec, times(1)).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void shouldNotRetry_when400BadRequestOccurs() {
            // Arrange
            WebClientResponseException badRequest = WebClientResponseException.create(
                    400, "Bad Request", HttpHeaders.EMPTY, new byte[0], null);

            // Reset the mock to avoid interference from setUp()
            reset(responseSpec);
            when(responseSpec.bodyToMono(ApacheAuthToken.class))
                    .thenReturn(Mono.error(badRequest));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectError(WebClientResponseException.class)
                    .verify();

            // Verify that the request was attempted only once (no retries)
            verify(responseSpec, times(1)).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void shouldNotRetry_when401UnauthorizedOccurs() {
            // Arrange
            WebClientResponseException unauthorized = WebClientResponseException.create(
                    401, "Unauthorized", HttpHeaders.EMPTY, new byte[0], null);

            // Reset the mock to avoid interference from setUp()
            reset(responseSpec);
            when(responseSpec.bodyToMono(ApacheAuthToken.class))
                    .thenReturn(Mono.error(unauthorized));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectError(WebClientResponseException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void shouldNotRetry_when403ForbiddenOccurs() {
            // Arrange
            WebClientResponseException forbidden = WebClientResponseException.create(
                    403, "Forbidden", HttpHeaders.EMPTY, new byte[0], null);

            // Reset the mock to avoid interference from setUp()
            reset(responseSpec);
            when(responseSpec.bodyToMono(ApacheAuthToken.class))
                    .thenReturn(Mono.error(forbidden));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectError(WebClientResponseException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void shouldNotRetry_when404NotFoundOccurs() {
            // Arrange
            WebClientResponseException notFound = WebClientResponseException.create(
                    404, "Not Found", HttpHeaders.EMPTY, new byte[0], null);

            // Reset the mock to avoid interference from setUp()
            reset(responseSpec);
            when(responseSpec.bodyToMono(ApacheAuthToken.class))
                    .thenReturn(Mono.error(notFound));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectError(WebClientResponseException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(ApacheAuthToken.class);
        }

        @Test
        void shouldNotRetry_whenRuntimeExceptionOccurs() {
            // Arrange
            RuntimeException runtimeException = new RuntimeException("Business logic error");

            // Reset the mock to avoid interference from setUp()
            reset(responseSpec);
            when(responseSpec.bodyToMono(ApacheAuthToken.class))
                    .thenReturn(Mono.error(runtimeException));

            // Act
            Mono<ApacheAuthToken> result = httpRequestHandler.post(url, headers, requestBody, ApacheAuthToken.class);

            // Assert
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(ApacheAuthToken.class);
        }
    }
}
