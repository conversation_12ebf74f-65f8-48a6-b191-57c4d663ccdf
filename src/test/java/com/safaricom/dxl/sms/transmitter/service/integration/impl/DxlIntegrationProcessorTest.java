package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.dxl.DxlSmsPayload;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DxlIntegrationProcessorTest {

    @Mock
    private DxLPayloadConstructor payloadConstructor;

    @Mock
    private DxlHeaderConstructor headerConstructor;

    @Mock
    private MsConfigProperties properties;

    @Mock
    private HttpRequestHandler httpRequestHandler;

    @Mock
    private DxlApiRespProcessor apiRespProcessor;

    @Mock
    private Utilities utilities;

    @InjectMocks
    private DxlIntegrationProcessor dxlIntegrationProcessor;

    private String sourceAddress;
    private String destinationAddress;
    private String message;
    private Map<String, String> headers;
    private DxlSmsPayload payload;
    private Consumer<HttpHeaders> httpHeaders;
    private WsResponse response;
    private WsResponse errorResponse;
    private LocalDateTime requestTime;

    @BeforeEach
    void setUp() {
        // Set up test data
        sourceAddress = "12345";
        destinationAddress = "254722000000";
        message = "Test message";
        headers = new HashMap<>();
        headers.put("X-Conversation-ID", "test-conversation-id");
        payload = new DxlSmsPayload();
        httpHeaders = httpHeader -> {};
        response = Stub.mockWsResponse("200", "Success", null);
        errorResponse = Stub.mockWsResponse("500", "Error", null);
        requestTime = LocalDateTime.now();

        // Mock dependencies
        when(properties.getDxlEndpoint()).thenReturn("https://dxl-endpoint.com");
        when(payloadConstructor.generatePayload(anyString(), anyString(), anyString(), any())).thenReturn(payload);
        when(headerConstructor.constructHeaders(anyString(), any())).thenReturn(Mono.just(httpHeaders));
        when(httpRequestHandler.post(anyString(), any(), anyString(), eq(WsResponse.class))).thenReturn(Mono.just(response));
        when(apiRespProcessor.processApiResponse(any(), anyString(), anyString(), anyString(), anyString(), any(), any())).thenReturn(Mono.just(response));
        when(utilities.handleExceptions(any(), any(), anyString(), anyString(), anyString(), any(), anyInt())).thenReturn(Mono.just(errorResponse));
    }

    @Nested
    class GeneratePayloadTests {

        @Test
        void generatePayload_shouldCallPayloadConstructor() {
            // Act & Assert
            StepVerifier.create(Mono.just(dxlIntegrationProcessor.generatePayload(sourceAddress, destinationAddress, message, headers)))
                    .expectNext(payload)
                    .verifyComplete();
        }
    }

    @Nested
    class GetEndpointTests {

        @Test
        void getEndpoint_shouldReturnConfiguredEndpoint() {
            // Act & Assert
            StepVerifier.create(Mono.just(dxlIntegrationProcessor.getEndpoint()))
                    .expectNext("https://dxl-endpoint.com")
                    .verifyComplete();
        }
    }

    @Nested
    class ConstructHeadersTests {

        @Test
        void constructHeaders_shouldCallHeaderConstructor() {
            // Act & Assert
            StepVerifier.create(dxlIntegrationProcessor.constructHeaders(destinationAddress, headers))
                    .expectNext(httpHeaders)
                    .verifyComplete();
        }
    }

    @Nested
    class GetResponseClassTests {

        @Test
        void getResponseClass_shouldReturnWsResponseClass() {
            // Act & Assert
            StepVerifier.create(Mono.just(dxlIntegrationProcessor.getResponseClass()))
                    .expectNext(WsResponse.class)
                    .verifyComplete();
        }
    }

    @Nested
    class ProcessResponseTests {

        @Test
        void processResponse_shouldCallApiRespProcessor() {
            // Act & Assert
            StepVerifier.create(dxlIntegrationProcessor.processResponse(response, sourceAddress, destinationAddress, "https://dxl-endpoint.com", message, requestTime, headers))
                    .expectNext(response)
                    .verifyComplete();
        }
    }

    @Nested
    class HandleErrorTests {

        @Test
        void handleError_shouldCallUtilitiesHandleExceptions() {
            // Arrange
            Throwable error = new RuntimeException("Test error");

            // Act & Assert
            StepVerifier.create(dxlIntegrationProcessor.handleError(error, sourceAddress, destinationAddress, message, requestTime, headers))
                    .expectNext(errorResponse)
                    .verifyComplete();
        }
    }
}
