package com.safaricom.dxl.sms.transmitter.exception;

import com.safaricom.dxl.webflux.starter.model.WsHeader;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsMappingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class MsExceptionHandlerTest {

    @Mock
    private WsMappingService mappingService;

    @Mock
    private ServerHttpRequest request;

    @Mock
    private HttpHeaders httpHeaders;

    @Spy
    @InjectMocks
    private MsExceptionHandler exceptionHandler;

    private ResponseEntity<WsResponse> mockResponseEntity;

    @BeforeEach
    void setUp() {
        // Setup mock response
        WsResponse mockWsResponse = new WsResponse();
        WsHeader header = new WsHeader();
        header.setResponseCode(500);
        header.setResponseMessage("Test Error");
        mockWsResponse.setHeader(header);

        mockResponseEntity = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(mockWsResponse);

        // Setup request headers
        Mockito.when(request.getHeaders()).thenReturn(httpHeaders);

        // Mock the setErrResponse method to return our mock response
        doReturn(Mono.just(mockResponseEntity))
                .when(exceptionHandler).setErrResponse(anyString(), anyString(), any(ServerHttpRequest.class), any(HttpStatus.class));
    }

    @Test
    void internalServerErrorException_shouldReturnCorrectResponse() {
        // Arrange
        InternalServerErrorException exception = new InternalServerErrorException("Server error", ERR_SERVER_ERROR);

        // Act
        Mono<ResponseEntity<WsResponse>> result = exceptionHandler.internalServerErrorException(exception, request);

        // Assert
        StepVerifier.create(result)
                .expectNext(mockResponseEntity)
                .verifyComplete();

        // Verify the correct method was called with the right parameters
        verify(exceptionHandler).setErrResponse(
                ERR_SERVER_ERROR,
                "Server error",
                request,
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Test
    void badRequestException_shouldReturnCorrectResponse() {
        // Arrange
        BadRequestException exception = new BadRequestException("Bad request", ERR_BAD_REQUEST);

        // Act
        Mono<ResponseEntity<WsResponse>> result = exceptionHandler.badRequestException(exception, request);

        // Assert
        StepVerifier.create(result)
                .expectNext(mockResponseEntity)
                .verifyComplete();

        // Verify the correct method was called with the right parameters
        verify(exceptionHandler).setErrResponse(
                ERR_BAD_REQUEST,
                "Bad request",
                request,
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void integrationException_shouldReturnCorrectResponse() {
        // Arrange
        IntegrationException exception = new IntegrationException("Integration error", ERR_INTEGRATION_ERROR, "TEST");

        // Act
        Mono<ResponseEntity<WsResponse>> result = exceptionHandler.integrationException(exception, request);

        // Assert
        StepVerifier.create(result)
                .expectNext(mockResponseEntity)
                .verifyComplete();

        // Verify the correct method was called with the right parameters
        verify(exceptionHandler).setErrResponse(
                ERR_INTEGRATION_ERROR,
                "Integration error",
                request,
                HttpStatus.SERVICE_UNAVAILABLE);
    }

    @Test
    void unauthorizedException_shouldReturnCorrectResponse() {
        // Arrange
        WsUnauthorizedException exception = new WsUnauthorizedException("Unauthorized", ERR_UNAUTHORIZED);

        // Act
        Mono<ResponseEntity<WsResponse>> result = exceptionHandler.unauthorizedException(exception, request);

        // Assert
        StepVerifier.create(result)
                .expectNext(mockResponseEntity)
                .verifyComplete();

        // Verify the correct method was called with the right parameters
        verify(exceptionHandler).setErrResponse(
                ERR_UNAUTHORIZED,
                "Unauthorized",
                request,
                HttpStatus.UNAUTHORIZED);
    }

    @Test
    void responseStatusException_shouldReturnCorrectResponse() {
        // Arrange
        ResponseStatusException exception = new ResponseStatusException(HttpStatus.NOT_FOUND, "Not found");

        // Act
        Mono<ResponseEntity<WsResponse>> result = exceptionHandler.responseStatusException(exception, request);

        // Assert
        StepVerifier.create(result)
                .expectNext(mockResponseEntity)
                .verifyComplete();

        // Verify the correct method was called with the right parameters
        verify(exceptionHandler).setErrResponse(
                "404",
                "Not found",
                request,
                HttpStatus.NOT_FOUND);
    }
}
