package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.component.HttpRequestHandler;
import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.datalayer.dao.ICacheAo;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.ApachePayload;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Response;
import com.safaricom.dxl.sms.transmitter.service.FallbackService;
import com.safaricom.dxl.sms.transmitter.service.StreamingService;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Consumer;

import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.ERR_INTEGRATION_ERROR;
import static com.safaricom.dxl.sms.transmitter.utils.GlobalVariables.TRANS_SEND_APACHE_SMS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ApacheIntegrationProcessorTest {

    @Mock
    private ApachePayloadConstructor payloadConstructor;

    @Mock
    private ApacheHeaderConstructor headerConstructor;

    @Mock
    private MsConfigProperties properties;

    @Mock
    private HttpRequestHandler httpRequestHandler;

    @Mock
    private TibcoApacheApiRespProcessor apiRespProcessor;

    @Mock
    private Utilities utilities;

    @Mock
    private StreamingService streamingService;

    @Mock
    private ICacheAo cacheAo;

    @Mock
    private FallbackService fallbackService;

    @InjectMocks
    private ApacheIntegrationProcessor apacheIntegrationProcessor;

    private String sourceAddress;
    private String destinationAddress;
    private String message;
    private Map<String, String> headers;
    private LocalDateTime requestTime;
    private ApachePayload apachePayload;
    private Response response;

    @BeforeEach
    void setUp() {
        sourceAddress = "12345";
        destinationAddress = "254722000000";
        message = "Test message";
        headers = Stub.mockHeaders();
        requestTime = Stub.mockDateTime();
        apachePayload = Stub.mockApachePayload();
        response = Stub.mockIntegrationResponse("00");
        Consumer<HttpHeaders> httpHeadersConsumer = httpHeaders -> {
        };

        when(properties.getApacheEndpoint()).thenReturn("https://test-apache-endpoint.com");
        when(properties.getApacheAuthTokenKey()).thenReturn("apache-auth-token");
        when(payloadConstructor.generatePayload(anyString(), anyString(), anyString(), any())).thenReturn(apachePayload);
        when(headerConstructor.constructHeaders(anyString(), any())).thenReturn(Mono.just(httpHeadersConsumer));
        when(httpRequestHandler.post(anyString(), any(), any(), eq(Response.class))).thenReturn(Mono.just(response));
        when(apiRespProcessor.processApiResponse(any(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.just(Stub.mockWsResponse("200", "Success", null)));
        when(utilities.handleExceptions(any(), any(), anyString(), anyString(), anyString(), any(), anyInt()))
                .thenReturn(Mono.just(Stub.mockWsResponse("500", "Error", null)));
        when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.just(Stub.mockWsResponse("200", "Success", null)));
        when(cacheAo.deleteCache(anyString())).thenReturn(Mono.empty());
    }

    @Nested
    class ProcessIntegrationRequestTests {

        @Test
        void processIntegrationRequest_withValidRequest_shouldCallDependencies() {
            // Act
            apacheIntegrationProcessor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            Mockito.verify(payloadConstructor).generatePayload(sourceAddress, destinationAddress, message, headers);
            Mockito.verify(headerConstructor).constructHeaders(destinationAddress, headers);
            Mockito.verify(httpRequestHandler).post(anyString(), any(), any(), eq(Response.class));
            Mockito.verify(apiRespProcessor).processApiResponse(any(), anyString(), anyString(), anyString(), anyString(), any(), any());
        }

        @Test
        void processIntegrationRequest_whenHttpRequestFails_shouldHandleError() {
            // Arrange
            when(httpRequestHandler.post(anyString(), any(), any(), eq(Response.class)))
                    .thenReturn(Mono.error(new RuntimeException("HTTP request failed")));

            // Act
            apacheIntegrationProcessor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    eq(sourceAddress), eq(destinationAddress), eq(message), eq("500"), anyString(), eq(requestTime), eq(headers));
            Mockito.verify(utilities).handleExceptions(
                    any(), any(), anyString(), anyString(), anyString(), any(RuntimeException.class), anyInt());
        }

        @Test
        void processIntegrationRequest_whenAuthTokenExpires_shouldClearCacheAndRetry() {
            // Arrange
            ResponseStatusException authError = new ResponseStatusException(org.springframework.http.HttpStatus.UNAUTHORIZED);
            when(properties.getMaxTokenRefreshRetries()).thenReturn(3);
            when(properties.getTokenRefreshRetryDelayMs()).thenReturn(1000L);
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());
            when(httpRequestHandler.post(anyString(), any(), any(), eq(Response.class)))
                    .thenReturn(Mono.error(authError))
                    .thenReturn(Mono.just(response));

            // Act
            apacheIntegrationProcessor.processIntegrationRequest(
                    sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    eq(sourceAddress), eq(destinationAddress), eq(message), eq("401"), anyString(), eq(requestTime), eq(headers));
            Mockito.verify(cacheAo).deleteCache("apache-auth-token");
            Mockito.verify(httpRequestHandler, Mockito.times(2)).post(anyString(), any(), any(), eq(Response.class));
        }
    }

    @Nested
    class GeneratePayloadTests {

        @Test
        void generatePayload_shouldDelegateToPayloadConstructor() {
            // Act
            ApachePayload result = apacheIntegrationProcessor.generatePayload(sourceAddress, destinationAddress, message, headers);

            // Assert
            assertEquals(apachePayload, result);
            Mockito.verify(payloadConstructor).generatePayload(sourceAddress, destinationAddress, message, headers);
        }
    }

    @Nested
    class GetEndpointTests {

        @Test
        void getEndpoint_shouldReturnConfiguredEndpoint() {
            // Act
            String result = apacheIntegrationProcessor.getEndpoint();

            // Assert
            assertEquals("https://test-apache-endpoint.com", result);
            Mockito.verify(properties).getApacheEndpoint();
        }
    }

    @Nested
    class ConstructHeadersTests {

        @Test
        void constructHeaders_shouldDelegateToHeaderConstructor() {
            // Act
            Mono<Consumer<HttpHeaders>> result = apacheIntegrationProcessor.constructHeaders(destinationAddress, headers);

            // Assert
            assertNotNull(result);
            Mockito.verify(headerConstructor).constructHeaders(destinationAddress, headers);
        }
    }

    @Nested
    class GetResponseClassTests {

        @Test
        void getResponseClass_shouldReturnResponseClass() {
            // Act
            Class<Response> result = apacheIntegrationProcessor.getResponseClass();

            // Assert
            assertEquals(Response.class, result);
        }
    }

    @Nested
    class ProcessResponseTests {

        @Test
        void processResponse_shouldDelegateToApiRespProcessor() {
            // Act
            apacheIntegrationProcessor.processResponse(
                    response, sourceAddress, destinationAddress, "endpoint", message, requestTime, headers);

            // Assert
            Mockito.verify(apiRespProcessor).processApiResponse(
                    response, sourceAddress, destinationAddress, "endpoint", message, requestTime, headers);
        }
    }

    @Nested
    class HandleErrorTests {

        @Test
        void handleError_withNormalError_shouldDelegateToUtilities() {
            // Arrange
            RuntimeException error = new RuntimeException("Test error");
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());

            // Act
            apacheIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers);

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    eq(sourceAddress), eq(destinationAddress), eq(message), eq("500"), anyString(), eq(requestTime), eq(headers));
            Mockito.verify(utilities).handleExceptions(
                    headers, requestTime, ERR_INTEGRATION_ERROR, "SMS API Call", TRANS_SEND_APACHE_SMS, error, 500);
        }

        @Test
        void handleError_withAuthError_shouldClearCacheAndRetry() {
            // Arrange
            ResponseStatusException error = new ResponseStatusException(org.springframework.http.HttpStatus.UNAUTHORIZED);
            when(properties.getMaxTokenRefreshRetries()).thenReturn(3);
            when(properties.getTokenRefreshRetryDelayMs()).thenReturn(1000L);
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());
            when(cacheAo.deleteCache(anyString())).thenReturn(Mono.empty());

            // Act
            apacheIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers);

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    eq(sourceAddress), eq(destinationAddress), eq(message), eq("401"), anyString(), eq(requestTime), eq(headers));
            Mockito.verify(cacheAo).deleteCache("apache-auth-token");
        }

        @Test
        void handleError_withInvalidTokenError_shouldClearCacheAndRetry() {
            // Arrange
            RuntimeException error = new RuntimeException("InvalidBearerToken: Token expired");
            when(properties.getMaxTokenRefreshRetries()).thenReturn(3);
            when(properties.getTokenRefreshRetryDelayMs()).thenReturn(1000L);
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());
            when(cacheAo.deleteCache(anyString())).thenReturn(Mono.empty());

            // Act
            apacheIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers);

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    eq(sourceAddress), eq(destinationAddress), eq(message), eq("401"), anyString(), eq(requestTime), eq(headers));
            Mockito.verify(cacheAo).deleteCache("apache-auth-token");
        }

        @Test
        void handleError_withAuthError_shouldClearCacheAndRetryThenSucceed() {
            // Arrange
            ResponseStatusException error = new ResponseStatusException(org.springframework.http.HttpStatus.UNAUTHORIZED);
            when(properties.getMaxTokenRefreshRetries()).thenReturn(3);
            when(properties.getTokenRefreshRetryDelayMs()).thenReturn(1000L);
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());
            when(cacheAo.deleteCache(anyString())).thenReturn(Mono.empty());
            when(httpRequestHandler.post(anyString(), any(), any(), eq(Response.class))).thenReturn(Mono.just(response));

            // Act
            WsResponse result = apacheIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            assertNotNull(result);
            assertEquals(200, result.getHeader().getResponseCode());

            // Verify cache was deleted
            Mockito.verify(cacheAo).deleteCache("apache-auth-token");
        }

        @Test
        void handleError_withNullErrorMessage_shouldHandleGracefully() {
            // Arrange
            RuntimeException error = new RuntimeException((String)null); // Null error message
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());

            // Act
            apacheIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers);

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    sourceAddress, destinationAddress, message, "500", "Unknown error", requestTime, headers);
            Mockito.verify(utilities).handleExceptions(
                    headers, requestTime, ERR_INTEGRATION_ERROR, "SMS API Call", TRANS_SEND_APACHE_SMS, error, 500);
        }

        @Test
        void handleError_withInvalidBearerTokenMessage_shouldClearCacheAndRetry() {
            // Arrange
            RuntimeException error = new RuntimeException("InvalidBearerToken: Token expired");
            when(properties.getMaxTokenRefreshRetries()).thenReturn(0); // Set to 0 to force immediate fallback
            when(properties.getApacheFallbackIntegrator()).thenReturn("TIBCO");
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());
            when(cacheAo.deleteCache(anyString())).thenReturn(Mono.empty());
            when(httpRequestHandler.post(anyString(), any(), any(), eq(Response.class))).thenReturn(Mono.just(response));
            when(properties.getApacheAuthTokenKey()).thenReturn("apache-auth-token");
            when(fallbackService.routeToFallbackIntegrator(anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.just(Stub.mockWsResponse("200", "Fallback Success", null)));

            // Act
            apacheIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    eq(sourceAddress), eq(destinationAddress), eq(message), eq("401"),
                    anyString(), eq(requestTime), eq(headers));
            // Use ArgumentCaptor to avoid mixing matchers and direct values
            ArgumentCaptor<String> integratorCaptor = ArgumentCaptor.forClass(String.class);
            ArgumentCaptor<String> sourceCaptor = ArgumentCaptor.forClass(String.class);
            ArgumentCaptor<String> destCaptor = ArgumentCaptor.forClass(String.class);
            ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
            ArgumentCaptor<LocalDateTime> timeCaptor = ArgumentCaptor.forClass(LocalDateTime.class);
            ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass(Map.class);

            Mockito.verify(fallbackService).routeToFallbackIntegrator(
                    integratorCaptor.capture(), sourceCaptor.capture(), destCaptor.capture(),
                    messageCaptor.capture(), timeCaptor.capture(), headersCaptor.capture());

            // Verify the captured values
            assertEquals("TIBCO", integratorCaptor.getValue());
            assertEquals(sourceAddress, sourceCaptor.getValue());
            assertEquals(destinationAddress, destCaptor.getValue());
            assertEquals(message, messageCaptor.getValue());
            assertEquals(requestTime, timeCaptor.getValue());
            assertEquals(headers, headersCaptor.getValue());
        }

        @Test
        void handleError_withNonAuthResponseStatusException_shouldHandleAsNormalError() {
            // Arrange
            ResponseStatusException error = new ResponseStatusException(org.springframework.http.HttpStatus.BAD_REQUEST, "Bad request");
            when(streamingService.streamSentSms(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Mono.empty());
            WsResponse mockResponse = Stub.mockWsResponse("200", "Success", null);
            when(utilities.handleExceptions(any(), any(), anyString(), anyString(), anyString(), any(), anyInt()))
                .thenReturn(Mono.just(mockResponse));

            // Act
            apacheIntegrationProcessor.handleError(
                    error, sourceAddress, destinationAddress, message, requestTime, headers).block();

            // Assert
            Mockito.verify(streamingService).streamSentSms(
                    eq(sourceAddress), eq(destinationAddress), eq(message), eq("500"), anyString(), eq(requestTime), eq(headers));
            Mockito.verify(utilities).handleExceptions(
                    headers, requestTime, ERR_INTEGRATION_ERROR, "SMS API Call", TRANS_SEND_APACHE_SMS, error, 500);

            // Verify that cache was not cleared
            Mockito.verify(cacheAo, Mockito.never()).deleteCache(anyString());
        }


    }
}
