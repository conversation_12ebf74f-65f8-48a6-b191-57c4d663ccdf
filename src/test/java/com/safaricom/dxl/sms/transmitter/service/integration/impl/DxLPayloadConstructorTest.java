package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import com.safaricom.dxl.sms.transmitter.model.pojo.dxl.DxlSmsPayload;
import com.safaricom.dxl.sms.transmitter.model.pojo.dxl.SenderReceiverDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DxLPayloadConstructorTest {

    @Mock
    private MsConfigProperties properties;

    @InjectMocks
    private DxLPayloadConstructor payloadConstructor;

    private String shortCode;
    private String destinationAddress;
    private String message;
    private Map<String, String> headers;

    @BeforeEach
    void setUp() {
        shortCode = "12345";
        destinationAddress = "254722000000";
        message = "Test message";
        headers = new HashMap<>();
        headers.put("X-Source-System", "TEST-SYSTEM");

        when(properties.getMessagePriority()).thenReturn("HIGH");
        when(properties.isMessageInteractive()).thenReturn(true);
        when(properties.getSmsCallbackUrl()).thenReturn("https://callback-url.com");
    }

    @Test
    void generatePayload_shouldCreateCorrectPayload() {
        // Act
        DxlSmsPayload payload = payloadConstructor.generatePayload(shortCode, destinationAddress, message, headers);

        // Assert
        assertNotNull(payload);
        assertEquals("sms", payload.getMessageType());
        assertEquals("sms", payload.getType());
        assertEquals("HIGH", payload.getPriority());
        assertEquals("true", payload.getInteractive());
        assertEquals("https://callback-url.com", payload.getCallBackUrl());
        assertEquals(message, payload.getContent());

        // Check sender details
        SenderReceiverDetails sender = payload.getSender();
        assertNotNull(sender);
        assertEquals("SMS".concat(shortCode), sender.getName());
        assertEquals(shortCode, sender.getPhoneNumber());

        // Check receiver details
        assertNotNull(payload.getReceiver());
        assertEquals(1, payload.getReceiver().size());
        SenderReceiverDetails receiver = payload.getReceiver().get(0);
        assertEquals("", receiver.getName());
        assertEquals(destinationAddress, receiver.getPhoneNumber());
    }

    @Test
    void generatePayload_withMissingSourceSystem_shouldUseDefaultValue() {
        // Arrange
        Map<String, String> headersWithoutSource = new HashMap<>();
        String expectedName = "SMS".concat(shortCode);

        // Act
        DxlSmsPayload payload = payloadConstructor.generatePayload(shortCode, destinationAddress, message, headersWithoutSource);

        // Assert
        assertNotNull(payload);
        SenderReceiverDetails sender = payload.getSender();
        assertNotNull(sender);
        assertEquals(expectedName, sender.getName());
    }
}
