package com.safaricom.dxl.sms.transmitter.service.impl;

import com.safaricom.dxl.sms.transmitter.datalayer.dao.IMenuForUserAo;
import com.safaricom.dxl.sms.transmitter.datalayer.entities.MenuForUser;
import com.safaricom.dxl.sms.transmitter.exception.BadRequestException;
import com.safaricom.dxl.sms.transmitter.exception.InternalServerErrorException;
import com.safaricom.dxl.sms.transmitter.model.pojo.Menu;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.model.pojo.MessageFormat;
import com.safaricom.dxl.sms.transmitter.service.MessageConversionService;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class MessageConversionServiceTest {

    @Mock
    private IMenuForUserAo menuForUserAo;

    @Mock
    private Utilities utilities;

    @InjectMocks
    private MessageConversionService messageConversionService;

    private Map<String, String> headers;
    private LocalDateTime requestTime;
    private String shortCode;
    private String phoneNumber;
    private Message textMessage;

    @BeforeEach
    void setUp() {
        headers = Stub.mockHeaders();
        requestTime = Stub.mockDateTime();
        shortCode = "12345";
        phoneNumber = "254722000000";
        textMessage = Stub.mockTextMessage();
    }

    @Nested
    class PrepareMessageFormatTests {

        @Test
        void prepareMessageFormat_withValidTextMessage_shouldReturnCorrectFormat() {
            // Act
            MessageFormat result = messageConversionService.prepareMessageFormat(textMessage, headers, requestTime);

            // Assert
            assertNotNull(result);
            assertEquals("This is a test message", result.getHeader());
            assertTrue(result.getMessage().isEmpty());
        }

        @Test
        void prepareMessageFormat_withValidButtonsMessage_shouldReturnCorrectFormat() {
            // Arrange
            Message message = new Message();
            message.setType("buttons");
            message.setTitle("Please select an option:");
            List<String> buttonOptions = new ArrayList<>();
            buttonOptions.add("Option 1");
            buttonOptions.add("Option 2");
            message.setDetails(buttonOptions);

            // Act
            MessageFormat result = messageConversionService.prepareMessageFormat(message, headers, requestTime);

            // Assert
            assertNotNull(result);
            assertEquals("Please select an option:", result.getHeader());
            assertFalse(result.getMessage().isEmpty());
            assertEquals(2, result.getMessage().size());
            assertEquals("Option 1", result.getMessage().get(0));
            assertEquals("Option 2", result.getMessage().get(1));
        }

        @Test
        void prepareMessageFormat_withNullMessageType_shouldThrowBadRequestException() {
            // Arrange
            Message message = Stub.mockTextMessage();
            message.setType(null);

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Message type cannot be null", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_withNullTextDetails_shouldThrowBadRequestException() {
            // Arrange
            Message message = Stub.mockTextMessage();
            message.setDetails(null);

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Message details cannot be null for text message", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_withInvalidTextDetailsType_shouldThrowBadRequestException() {
            // Arrange
            Message message = Stub.mockTextMessage();
            message.setDetails(new ArrayList<>());

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Details Type is invalid for text message", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_withUnsupportedMessageType_shouldThrowBadRequestException() {
            // Arrange
            Message message = Stub.mockTextMessage();
            message.setType("unsupported");

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Unsupported message type: unsupported", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_withNullButtonsDetails_shouldThrowBadRequestException() {
            // Arrange
            Message message = new Message();
            message.setType("buttons");
            message.setTitle("Please select an option:");
            message.setDetails(null);

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Message details cannot be null for buttons message", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_withInvalidButtonsDetailsType_shouldThrowBadRequestException() {
            // Arrange
            Message message = new Message();
            message.setType("buttons");
            message.setTitle("Please select an option:");
            message.setDetails("Invalid details type");

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Details Type is invalid for buttons message", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_withEmptyButtonsList_shouldThrowBadRequestException() {
            // Arrange
            Message message = new Message();
            message.setType("buttons");
            message.setTitle("Please select an option:");
            message.setDetails(new ArrayList<>());

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Buttons list cannot be empty", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_withNullDetails_shouldThrowBadRequestException() {
            // Arrange
            Message message = new Message();
            message.setType("text");
            // Deliberately not setting details to trigger validation error

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertTrue(exception.getMessage().contains("Message details cannot be null for text message"));
        }

        @Test
        void prepareMessageFormat_withInvalidDetailsType_shouldThrowBadRequestException() {
            // Arrange
            Message message = new Message();
            message.setType("text");
            // Set details to a type that will cause ClassCastException when cast to String
            message.setDetails(new ArrayList<>());

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Details Type is invalid for text message", exception.getMessage());
        }

        @ParameterizedTest(name = "{0}")
        @MethodSource("validationErrorCases")
        void prepareMessageFormat_withValidationErrors_shouldThrowBadRequestException(
                String testName, Message message, String expectedErrorMessage) {
            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals(expectedErrorMessage, exception.getMessage());
        }

        static Stream<Arguments> validationErrorCases() {
            return Stream.of(
                Arguments.of("Null Button Value", createButtonsMessage("Please select an option:", Collections.singletonList(null)),
                    "Button value cannot be null"),
                Arguments.of("Null Title", createButtonsMessage(null, Collections.singletonList("Option 1")),
                    "Title cannot be null or empty for buttons message"),
                Arguments.of("Empty Title", createButtonsMessage("  ", Collections.singletonList("Option 1")),
                    "Title cannot be null or empty for buttons message"),
                Arguments.of("Button With Invalid Characters", createButtonsMessage("Title", Collections.singletonList("\u0000")),
                    "Button value contains invalid characters: \u0000")
            );
        }

        @Test
        void prepareMessageFormat_withTextMessageContainingInvalidCharacters_shouldThrowBadRequestException() {
            // Arrange
            Message message = new Message();
            message.setType("text");
            message.setDetails("\u0000"); // Null character which should be invalid

            // No need to mock utilities.logMessage as it returns void

            // Act & Assert
            BadRequestException exception = assertThrows(BadRequestException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertEquals("Details value contains invalid characters", exception.getMessage());
        }

        @Test
        void prepareMessageFormat_whenNullPointerExceptionOccurs_shouldHandleMessageFormatError() {
            // Arrange
            Message message = null; // This will cause a NullPointerException

            // Act & Assert
            InternalServerErrorException exception = assertThrows(InternalServerErrorException.class, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            // Verify the exception was handled by handleMessageFormatError
            assertTrue(exception.getMessage().contains("Null pointer exception while preparing message format"));
        }

        @Test
        void prepareMessageFormat_whenGeneralExceptionOccurs_shouldHandleMessageFormatError() {
            // Arrange
            Message message = new Message();
            message.setType("text");
            message.setDetails("Some details");

            // Create a spy of the messageConversionService to throw an exception
            MessageConversionService spyService = Mockito.spy(messageConversionService);
            doThrow(new RuntimeException("Unexpected error")).when(spyService).checkUnwantedCharacters(anyString());

            // Act & Assert
            InternalServerErrorException exception = assertThrows(InternalServerErrorException.class, () ->
                spyService.prepareMessageFormat(message, headers, requestTime));

            // Verify the exception was handled by handleMessageFormatError
            assertTrue(exception.getMessage().contains("Unexpected error while preparing message format"));
        }

        private static Message createButtonsMessage(String title, List<String> buttonOptions) {
            Message message = new Message();
            message.setType("buttons");
            message.setTitle(title);
            message.setDetails(buttonOptions);
            return message;
        }

        @ParameterizedTest(name = "{0}")
        @MethodSource("exceptionHandlingCases")
        void prepareMessageFormat_withExceptions_shouldHandleGracefully(
                String testName, Message message, String expectedErrorMessageContains, Class<? extends Exception> expectedExceptionType) {
            // Act & Assert
            Exception exception = assertThrows(expectedExceptionType, () ->
                messageConversionService.prepareMessageFormat(message, headers, requestTime));

            assertTrue(exception.getMessage().contains(expectedErrorMessageContains),
                    "Expected error message to contain '" + expectedErrorMessageContains + "' but was '" + exception.getMessage() + "'");
        }

        static Stream<Arguments> exceptionHandlingCases() {
            // Create a message that will cause a NullPointerException
            Message nullDetailsMessage = new Message();
            nullDetailsMessage.setType("text");
            // Don't set details or other required fields

            // Create a message that will cause a ClassCastException
            Message classCastMessage = Stub.mockTextMessage();
            Map<String, Object> invalidDetails = new HashMap<>();
            invalidDetails.put("key", "value");
            classCastMessage.setDetails(invalidDetails); // This will cause ClassCastException when trying to cast to String

            return Stream.of(
                Arguments.of("Null Pointer Exception", nullDetailsMessage,
                    "Message details cannot be null for text message", BadRequestException.class),
                Arguments.of("Class Cast Exception", classCastMessage,
                    "Invalid data type in message payload", BadRequestException.class)
            );
        }
    }

    @Nested
    class ConvertMenuToTextFormatTests {

        @Test
        void convertMenuToTextFormat_withValidMenu_shouldReturnFormattedText() {
            // Arrange
            List<String> buttons = new ArrayList<>();
            buttons.add("Option 1");
            buttons.add("Option 2");

            String testHeader = "Header Text";

            MenuForUser menuForUser = MenuForUser.builder()
                    .userId(phoneNumber)
                    .shortCode(shortCode)
                    .menu(Arrays.asList(new Menu("1", "Option 1"), new Menu("2", "Option 2")))
                    .available(true)
                    .created(LocalDateTime.now())
                    .build();

            when(menuForUserAo.getMenuByUserIdAndShortCode(phoneNumber, shortCode))
                    .thenReturn(Mono.just(menuForUser));
            when(menuForUserAo.update(any(MenuForUser.class)))
                    .thenReturn(Mono.just(menuForUser));
            when(menuForUserAo.add(any(MenuForUser.class)))
                    .thenReturn(Mono.just(menuForUser));

            // Act
            Mono<String> result = messageConversionService.convertMenuToTextFormat(
                    testHeader, shortCode, buttons, phoneNumber, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectNextMatches(text ->
                    text.contains("Header Text") &&
                    text.contains("1. Option 1") &&
                    text.contains("2. Option 2"))
                .verifyComplete();
        }

        @Test
        void convertMenuToTextFormat_withMainMenuAndExitOptions_shouldUseSpecialCodes() {
            // Arrange
            List<String> buttons = new ArrayList<>();
            buttons.add("Main Menu");
            buttons.add("Option 1");
            buttons.add("Exit");

            String testHeader = "Header Text";

            MenuForUser menuForUser = MenuForUser.builder()
                    .userId(phoneNumber)
                    .shortCode(shortCode)
                    .menu(Arrays.asList(
                        new Menu("0", "Main Menu"),
                        new Menu("1", "Option 1"),
                        new Menu("00", "Exit")))
                    .available(true)
                    .created(LocalDateTime.now())
                    .build();

            when(menuForUserAo.getMenuByUserIdAndShortCode(phoneNumber, shortCode))
                    .thenReturn(Mono.just(menuForUser));
            when(menuForUserAo.update(any(MenuForUser.class)))
                    .thenReturn(Mono.just(menuForUser));
            when(menuForUserAo.add(any(MenuForUser.class)))
                    .thenReturn(Mono.just(menuForUser));

            // Act
            Mono<String> result = messageConversionService.convertMenuToTextFormat(
                    testHeader, shortCode, buttons, phoneNumber, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectNextMatches(text -> {
                    // Print the actual text for debugging
                    System.out.println("Actual text: " + text);
                    return text.contains("Header Text") &&
                           text.contains("0. Main Menu") &&
                           // The index might be different in the actual implementation
                           text.contains("Option 1") &&
                           text.contains("00. Exit");
                })
                .verifyComplete();
        }

        @Test
        void convertMenuToTextFormat_whenMenuDoesNotExist_shouldCreateNewMenu() {
            // Arrange
            List<String> buttons = new ArrayList<>();
            buttons.add("Option 1");
            buttons.add("Option 2");

            String testHeader = "Header Text";

            MenuForUser menuForUser = MenuForUser.builder()
                    .userId(phoneNumber)
                    .shortCode(shortCode)
                    .menu(Arrays.asList(new Menu("1", "Option 1"), new Menu("2", "Option 2")))
                    .available(true)
                    .created(LocalDateTime.now())
                    .build();

            when(menuForUserAo.getMenuByUserIdAndShortCode(phoneNumber, shortCode))
                    .thenReturn(Mono.empty());
            when(menuForUserAo.add(any(MenuForUser.class)))
                    .thenReturn(Mono.just(menuForUser));

            // Act
            Mono<String> result = messageConversionService.convertMenuToTextFormat(
                    testHeader, shortCode, buttons, phoneNumber, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectNextMatches(text ->
                    text.contains("Header Text") &&
                    text.contains("1. Option 1") &&
                    text.contains("2. Option 2"))
                .verifyComplete();
        }

        @Test
        void convertMenuToTextFormat_whenUpdateFails_shouldReturnError() {
            // Arrange
            List<String> buttons = new ArrayList<>();
            buttons.add("Option 1");

            String testHeader = "Header Text";

            MenuForUser menuForUser = MenuForUser.builder()
                    .userId(phoneNumber)
                    .shortCode(shortCode)
                    .menu(Collections.singletonList(new Menu("1", "Option 1")))
                    .available(true)
                    .created(LocalDateTime.now())
                    .build();

            when(menuForUserAo.getMenuByUserIdAndShortCode(phoneNumber, shortCode))
                    .thenReturn(Mono.just(menuForUser));
            when(menuForUserAo.update(any(MenuForUser.class)))
                    .thenReturn(Mono.error(new RuntimeException("Update failed")));
            when(menuForUserAo.add(any(MenuForUser.class)))
                    .thenReturn(Mono.just(menuForUser));

            // Act
            Mono<String> result = messageConversionService.convertMenuToTextFormat(
                    testHeader, shortCode, buttons, phoneNumber, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof InternalServerErrorException &&
                    error.getMessage().contains("Failed to update menu for user"))
                .verify();
        }

        @Test
        void convertMenuToTextFormat_whenAddFails_shouldReturnError() {
            // Arrange
            List<String> buttons = new ArrayList<>();
            buttons.add("Option 1");

            String testHeader = "Header Text";

            when(menuForUserAo.getMenuByUserIdAndShortCode(phoneNumber, shortCode))
                    .thenReturn(Mono.empty());
            when(menuForUserAo.add(any(MenuForUser.class)))
                    .thenReturn(Mono.error(new RuntimeException("Add failed")));

            // Act
            Mono<String> result = messageConversionService.convertMenuToTextFormat(
                    testHeader, shortCode, buttons, phoneNumber, headers, requestTime);

            // Assert
            StepVerifier.create(result)
                .expectErrorMatches(error ->
                    error instanceof InternalServerErrorException &&
                    error.getMessage().contains("Failed to add new menu for user"))
                .verify();
        }
    }

    @Nested
    class DecodeSpecialCharactersTests {

        @Test
        void decodeSpecialCharacters_shouldReplaceSpecialCharacters() {
            // Arrange
            String text = "It's a test";

            // Act
            Mono<String> result = messageConversionService.decodeSpecialCharacters(text);

            // Assert
            StepVerifier.create(result)
                .expectNext("It's a test")
                .verifyComplete();
        }

        @Test
        void decodeSpecialCharacters_withNoSpecialCharacters_shouldReturnSameText() {
            // Arrange
            String text = "Its a test";

            // Act
            Mono<String> result = messageConversionService.decodeSpecialCharacters(text);

            // Assert
            StepVerifier.create(result)
                .expectNext("Its a test")
                .verifyComplete();
        }
    }

    @Nested
    class CheckUnwantedCharactersTests {

        @Test
        void checkUnwantedCharacters_withValidCharacters_shouldReturnFalse() {
            // Arrange
            String input = "Valid text 123 !@#$%^&*()_+";

            // Act
            boolean result = messageConversionService.checkUnwantedCharacters(input);

            // Assert
            assertFalse(result);
        }

        @Test
        void checkUnwantedCharacters_withNullInput_shouldReturnTrue() {
            // Act
            boolean result = messageConversionService.checkUnwantedCharacters(null);

            // Assert
            assertTrue(result);
        }

        @Test
        void checkUnwantedCharacters_withEmptyInput_shouldReturnTrue() {
            // Act
            boolean result = messageConversionService.checkUnwantedCharacters("");

            // Assert
            assertTrue(result);
        }

        @Test
        void checkUnwantedCharacters_withInvalidCharacters_shouldReturnTrue() {
            // Arrange
            String input = "Invalid text with emoji 😊";

            // Act
            boolean result = messageConversionService.checkUnwantedCharacters(input);

            // Assert
            assertTrue(result);
        }
    }

    // We can't directly test handleConversionError because it's protected
    // Instead, we test it indirectly through convertMenuToTextFormat_whenAddFails_shouldReturnError
    // and convertMenuToTextFormat_whenUpdateFails_shouldReturnError

    @Test
    void convertMenuToTextFormat_whenSaveMenuFails_shouldReturnError() {
        // Arrange
        String testHeader = "Please select an option:";
        // Using class-level shortCode field instead of creating a local variable
        List<String> buttons = Arrays.asList("Option 1", "Option 2");
        String testPhoneNumber = "254712345678";
        when(menuForUserAo.getMenuByUserIdAndShortCode(anyString(), anyString())).thenReturn(Mono.empty());
        when(menuForUserAo.add(any(MenuForUser.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(messageConversionService.convertMenuToTextFormat(testHeader, shortCode, buttons, testPhoneNumber, headers, requestTime))
            .expectErrorMatches(error -> error instanceof InternalServerErrorException &&
                                      error.getMessage().contains("Failed to save or convert menu for user"))
            .verify();
    }

    @Test
    void convertMenuToTextFormat_whenSaveMenuThrowsException_shouldPropagateError() {
        // Arrange
        String testHeader = "Please select an option:";
        // Using class-level shortCode field instead of creating a local variable
        List<String> buttons = Arrays.asList("Option 1", "Option 2");
        String testPhoneNumber = "254712345678";
        when(menuForUserAo.getMenuByUserIdAndShortCode(anyString(), anyString()))
            .thenReturn(Mono.error(new RuntimeException("Database error")));
        when(menuForUserAo.add(any(MenuForUser.class)))
            .thenReturn(Mono.just(new MenuForUser()));

        // Act & Assert
        StepVerifier.create(messageConversionService.convertMenuToTextFormat(testHeader, shortCode, buttons, testPhoneNumber, headers, requestTime))
            .expectErrorMatches(error -> error instanceof InternalServerErrorException &&
                                      error.getMessage().contains("Database error"))
            .verify();
    }

    @Test
    void prepareMessageFormat_whenDetailsIsNull_shouldThrowBadRequestException() {
        // Arrange
        Message message = new Message();
        message.setType("text");
        message.setDetails(null);
        // Using class-level headers and requestTime fields

        // Act & Assert
        BadRequestException exception = assertThrows(BadRequestException.class, () ->
            messageConversionService.prepareMessageFormat(message, headers, requestTime));

        assertEquals("Message details cannot be null for text message", exception.getMessage());
    }

    @Test
    void prepareMessageFormat_whenInvalidDetailsType_shouldThrowBadRequestException() {
        // Arrange
        Message message = new Message();
        message.setType("text");
        // Setting details to an invalid type to trigger ClassCastException
        message.setDetails(Arrays.asList("item1", "item2")); // This will cause a ClassCastException when trying to cast to String
        // Using class-level headers and requestTime fields

        // Act & Assert
        BadRequestException exception = assertThrows(BadRequestException.class, () ->
            messageConversionService.prepareMessageFormat(message, headers, requestTime));

        assertEquals("Details Type is invalid for text message", exception.getMessage());
    }
}
