package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;

import com.safaricom.dxl.sms.transmitter.datalayer.entities.OutboundSMSMetrics;
import com.safaricom.dxl.sms.transmitter.datalayer.repositories.OutboundSMSMetricsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OutboundSMSMetricsAoImplTest {

    @Mock
    private OutboundSMSMetricsRepository outboundSMSMetricsRepository;

    @InjectMocks
    private OutboundSMSMetricsAoImpl outboundSMSMetricsAo;

    @Captor
    private ArgumentCaptor<OutboundSMSMetrics> metricsCaptor;

    private OutboundSMSMetrics smsMetrics;

    @BeforeEach
    void setUp() {
        smsMetrics = OutboundSMSMetrics.builder()
                .id("test-id")
                .type("SMS")
                .shortCode("12345")
                .phoneNumber("254722000000")
                .message("Test message")
                .apiResCode("200")
                .apiResMessage("Success")
                .latency("100ms")
                .serviceName("test-service")
                .apiResponseTime(LocalDateTime.now())
                .build();
    }

    @Test
    void addSMSMetrics_withValidInput_shouldSetCreatedTimeAndReturnSavedMetrics() {
        // Arrange
        when(outboundSMSMetricsRepository.save(any(OutboundSMSMetrics.class))).thenReturn(Mono.just(smsMetrics));

        // Act
        Mono<OutboundSMSMetrics> result = outboundSMSMetricsAo.addSMSMetrics(smsMetrics);

        // Assert
        StepVerifier.create(result)
                .expectNext(smsMetrics)
                .verifyComplete();

        verify(outboundSMSMetricsRepository).save(metricsCaptor.capture());
        OutboundSMSMetrics capturedMetrics = metricsCaptor.getValue();
        assertNotNull(capturedMetrics.getCreated());
    }

    @Test
    void addSMSMetrics_withError_shouldPropagateError() {
        // Arrange
        when(outboundSMSMetricsRepository.save(any(OutboundSMSMetrics.class)))
                .thenReturn(Mono.error(new RuntimeException("Database error")));

        // Act & Assert
        StepVerifier.create(outboundSMSMetricsAo.addSMSMetrics(smsMetrics))
                .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                        throwable.getMessage().equals("Database error"))
                .verify();
    }
}
