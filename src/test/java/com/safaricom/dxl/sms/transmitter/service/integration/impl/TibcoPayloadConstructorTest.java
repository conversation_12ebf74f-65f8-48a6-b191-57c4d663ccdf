package com.safaricom.dxl.sms.transmitter.service.integration.impl;

import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.TibcoMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class TibcoPayloadConstructorTest {

    @InjectMocks
    private TibcoPayloadConstructor payloadConstructor;

    private String shortCode;
    private String destinationAddress;
    private String message;
    private Map<String, String> headers;

    @BeforeEach
    void setUp() {
        shortCode = "12345";
        destinationAddress = "254722000000";
        message = "Test message";
        headers = new HashMap<>();
    }

    @Test
    void generatePayload_shouldCreateCorrectPayload() {
        // Act
        TibcoMessage payload = payloadConstructor.generatePayload(shortCode, destinationAddress, message, headers);

        // Assert
        assertNotNull(payload);
        
        // Check roles
        assertNotNull(payload.getRoles());
        assertNotNull(payload.getRoles().getReceiver());
        assertNotNull(payload.getRoles().getReceiver().getId());
        assertEquals(1, payload.getRoles().getReceiver().getId().size());
        assertEquals(destinationAddress, payload.getRoles().getReceiver().getId().get(0).getValue());
        
        // Check parts
        assertNotNull(payload.getParts());
        assertNotNull(payload.getParts().getBody());
        assertEquals(message, payload.getParts().getBody().getText());
        assertNotNull(payload.getParts().getTrailer());
        assertEquals(shortCode, payload.getParts().getTrailer().getText());
    }
}
