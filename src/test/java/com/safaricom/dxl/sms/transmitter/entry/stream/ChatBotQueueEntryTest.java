package com.safaricom.dxl.sms.transmitter.entry.stream;

import com.safaricom.dxl.sms.transmitter.model.dto.ChatBotRequest;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.model.pojo.Recipient;
import com.safaricom.dxl.sms.transmitter.service.SmsRequestRouter;
import com.safaricom.dxl.sms.transmitter.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import stub.Stub;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ChatBotQueueEntryTest {

    @Mock
    private SmsRequestRouter smsRequestRouter;

    @Mock
    private Utilities utilities;

    @InjectMocks
    private ChatBotQueueEntry chatBotQueueEntry;

    private ChatBotRequest chatBotRequest;
    private Map<String, String> headers;
    private WsResponse successResponse;

    @BeforeEach
    void setUp() {
        // Create a valid ChatBot request
        chatBotRequest = new ChatBotRequest();
        chatBotRequest.setCorrelationId("test-correlation-id");

        Recipient recipient = new Recipient();
        recipient.setAppSecretId("12345");
        recipient.setUserId("254712345678");
        recipient.setAppName("test-app");
        recipient.setAuthToken("test-auth-token");
        chatBotRequest.setRecipient(recipient);

        Message message = new Message();
        message.setType("text");
        message.setDetails("Test message");
        chatBotRequest.setMessage(message);

        // Create headers
        headers = new HashMap<>();
        headers.put("X-Conversation-ID", "test-conversation-id");
        headers.put("X-Source-System", "test-source-system");

        // Create success response
        successResponse = Stub.mockWsResponse("202", "Request accepted", null);

        // Mock utilities - use lenient to avoid unnecessary stubbing errors
        Mockito.lenient().when(utilities.createHeadersWithDefaults(anyString(), anyString(), anyString()))
                .thenReturn(headers);

        // Mock the router - use lenient to avoid unnecessary stubbing errors
        Mockito.lenient().when(smsRequestRouter.handleChatBotRequest(any(ChatBotRequest.class), any(LocalDateTime.class), anyMap()))
                .thenReturn(Mono.just(successResponse));
    }

    @Test
    void receiveMessageFromChatBot_shouldProcessMessageSuccessfully() {
        // Act & Assert
        StepVerifier.create(chatBotQueueEntry.receiveMessageFromChatBot(chatBotRequest))
                .verifyComplete();
    }

    @Test
    void receiveMessageFromChatBot_whenErrorOccurs_shouldHandleGracefully() {
        // Arrange
        RuntimeException testException = new RuntimeException("Test error");
        when(smsRequestRouter.handleChatBotRequest(any(ChatBotRequest.class), any(LocalDateTime.class), anyMap()))
                .thenReturn(Mono.error(testException));

        // Act - Just call the method and verify it doesn't throw an exception
        chatBotQueueEntry.receiveMessageFromChatBot(chatBotRequest).subscribe();

        // Assert - If we get here without an exception, the test passes
        assertTrue(true, "Method should handle errors gracefully");

        // Verify that the router was called with the correct parameters
        Mockito.verify(smsRequestRouter).handleChatBotRequest(eq(chatBotRequest), any(LocalDateTime.class), anyMap());
    }
}
