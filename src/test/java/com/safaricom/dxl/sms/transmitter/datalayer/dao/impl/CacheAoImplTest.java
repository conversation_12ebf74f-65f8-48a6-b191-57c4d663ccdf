package com.safaricom.dxl.sms.transmitter.datalayer.dao.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CacheAoImplTest {

    @Mock
    private ReactiveRedisTemplate<String, Object> redisTemplate;

    @Mock
    private ReactiveValueOperations<String, Object> valueOperations;

    @InjectMocks
    private CacheAoImpl cacheAo;

    private final String testKey = "test-key";
    private final String testValue = "test-value";
    private final Duration testDuration = Duration.ofMinutes(5);

    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Nested
    class SetCacheTests {

        @Test
        void setCache_withValidInput_shouldReturnTrue() {
            // Arrange
            when(valueOperations.set(eq(testKey), anyString(), eq(testDuration))).thenReturn(Mono.just(true));

            // Act & Assert
            StepVerifier.create(cacheAo.setCache(testKey, testValue, testDuration))
                    .expectNext(true)
                    .verifyComplete();
        }

        @Test
        void setCache_withValidInput_shouldReturnFalse() {
            // Arrange
            when(valueOperations.set(eq(testKey), anyString(), eq(testDuration))).thenReturn(Mono.just(false));

            // Act & Assert
            StepVerifier.create(cacheAo.setCache(testKey, testValue, testDuration))
                    .expectNext(false)
                    .verifyComplete();
        }

        @Test
        void setCache_withError_shouldPropagateError() {
            // Arrange
            when(valueOperations.set(eq(testKey), anyString(), eq(testDuration)))
                    .thenReturn(Mono.error(new RuntimeException("Redis error")));

            // Act & Assert
            StepVerifier.create(cacheAo.setCache(testKey, testValue, testDuration))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Redis error"))
                    .verify();
        }
    }

    @Nested
    class GetCacheTests {

        @Test
        void getCache_withExistingKey_shouldReturnValue() {
            // Arrange
            when(valueOperations.get(testKey)).thenReturn(Mono.just("\"test-value\""));

            // Act & Assert
            StepVerifier.create(cacheAo.getCache(testKey, String.class))
                    .expectNext(testValue)
                    .verifyComplete();
        }

        @Test
        void getCache_withNonExistingKey_shouldReturnEmpty() {
            // Arrange
            when(valueOperations.get(testKey)).thenReturn(Mono.empty());

            // Act & Assert
            StepVerifier.create(cacheAo.getCache(testKey, String.class))
                    .verifyComplete();
        }

        @Test
        void getCache_withError_shouldPropagateError() {
            // Arrange
            when(valueOperations.get(testKey))
                    .thenReturn(Mono.error(new RuntimeException("Redis error")));

            // Act & Assert
            StepVerifier.create(cacheAo.getCache(testKey, String.class))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Redis error"))
                    .verify();
        }
    }

    @Nested
    class DeleteCacheTests {

        @Test
        void deleteCache_withExistingKey_shouldReturnTrue() {
            // Arrange
            when(valueOperations.delete(testKey)).thenReturn(Mono.just(true));

            // Act & Assert
            StepVerifier.create(cacheAo.deleteCache(testKey))
                    .expectNext(true)
                    .verifyComplete();
        }

        @Test
        void deleteCache_withNonExistingKey_shouldReturnFalse() {
            // Arrange
            when(valueOperations.delete(testKey)).thenReturn(Mono.just(false));

            // Act & Assert
            StepVerifier.create(cacheAo.deleteCache(testKey))
                    .expectNext(false)
                    .verifyComplete();
        }

        @Test
        void deleteCache_withError_shouldPropagateError() {
            // Arrange
            when(valueOperations.delete(testKey))
                    .thenReturn(Mono.error(new RuntimeException("Redis error")));

            // Act & Assert
            StepVerifier.create(cacheAo.deleteCache(testKey))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                            throwable.getMessage().equals("Redis error"))
                    .verify();
        }
    }
}
