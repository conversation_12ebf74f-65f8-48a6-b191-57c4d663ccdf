package stub;

import com.safaricom.dxl.sms.transmitter.datalayer.entities.Apps;
import com.safaricom.dxl.sms.transmitter.datalayer.entities.OutboundSMSMetrics;
import com.safaricom.dxl.sms.transmitter.model.dto.ChatBotRequest;
import com.safaricom.dxl.sms.transmitter.model.dto.SmsRequest;
import com.safaricom.dxl.sms.transmitter.model.pojo.Message;
import com.safaricom.dxl.sms.transmitter.model.pojo.MessageFormat;
import com.safaricom.dxl.sms.transmitter.model.pojo.Recipient;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibco.*;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.CharacteristicValue;
import com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Response;
import com.safaricom.dxl.webflux.starter.model.WsHeader;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.*;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

/**
 * Utility class for creating test data.
 * This class provides static methods to create mock objects for testing.
 */
public class Stub {

    /**
     * Creates a mock SmsRequest object.
     *
     * @return A mock SmsRequest object
     */
    public static SmsRequest mockSmsRequest() {
        SmsRequest smsRequest = new SmsRequest();
        smsRequest.setShortCode("12345");
        smsRequest.setCustomerPhoneNumber("************");
        smsRequest.setMessage(mockTextMessage());
        return smsRequest;
    }

    /**
     * Creates a mock ChatBotRequest object.
     *
     * @return A mock ChatBotRequest object
     */
    public static ChatBotRequest mockChatBotRequest() {
        ChatBotRequest chatBotRequest = new ChatBotRequest();
        chatBotRequest.setRecipient(mockRecipient());
        chatBotRequest.setMessage(mockTextMessage());
        return chatBotRequest;
    }

    /**
     * Creates a mock Recipient object.
     *
     * @return A mock Recipient object
     */
    public static Recipient mockRecipient() {
        Recipient recipient = new Recipient();
        recipient.setAppSecretId("12345");
        recipient.setUserId("************");
        return recipient;
    }

    /**
     * Creates a mock text Message object.
     *
     * @return A mock Message object with text type
     */
    public static Message mockTextMessage() {
        Message message = new Message();
        message.setType("text");
        message.setDetails("This is a test message");
        return message;
    }

    /**
     * Creates a mock menu Message object.
     *
     * @return A mock Message object with menu type
     */
    public static Message mockMenuMessage() {
        Message message = new Message();
        message.setType("menu");

        Map<String, Object> details = new HashMap<>();
        details.put("header", "Please select an option:");

        List<Map<String, String>> buttons = new ArrayList<>();
        Map<String, String> button1 = new HashMap<>();
        button1.put("id", "1");
        button1.put("title", "Option 1");

        Map<String, String> button2 = new HashMap<>();
        button2.put("id", "2");
        button2.put("title", "Option 2");

        buttons.add(button1);
        buttons.add(button2);

        details.put("buttons", buttons);
        message.setDetails(details);

        return message;
    }

    /**
     * Creates a mock MessageFormat object.
     *
     * @return A mock MessageFormat object
     */
    public static MessageFormat mockMessageFormat() {
        List<Object> message = new ArrayList<>();
        Map<String, String> option1 = new HashMap<>();
        option1.put("id", "1");
        option1.put("title", "Option 1");
        message.add(option1);

        Map<String, String> option2 = new HashMap<>();
        option2.put("id", "2");
        option2.put("title", "Option 2");
        message.add(option2);

        return new MessageFormat(message, "Please select an option:");
    }

    /**
     * Creates a mock Apps object.
     *
     * @return A mock Apps object
     */
    public static Apps mockApp() {
        Apps app = new Apps();
        app.setAppSecretId("12345");
        app.setAppName("TestApp");
        app.setAuthToken("auth-token-123");
        app.setCreated(new Date());
        return app;
    }

    /**
     * Creates a mock OutboundSMSMetrics object.
     *
     * @return A mock OutboundSMSMetrics object
     */
    public static OutboundSMSMetrics mockOutboundSMSMetrics() {
        return OutboundSMSMetrics.builder()
                .type("OUTBOUND")
                .serviceName("SMS_TRANSMITTER")
                .shortCode("12345")
                .phoneNumber("************")
                .message("This is a test message")
                .created(LocalDateTime.now())
                .apiResCode("00")
                .apiResMessage("Success")
                .latency("100")
                .apiResponseTime(LocalDateTime.now())
                .build();
    }

    /**
     * Creates a mock Response object for Tibco/Apache integration.
     *
     * @param responseCode The response code to include
     * @return A mock Response object
     */
    public static Response mockIntegrationResponse(String responseCode) {
        CharacteristicValue codeValue = CharacteristicValue.builder()
                .characteristicName("ResponseCode")
                .value(responseCode)
                .build();

        CharacteristicValue descValue = CharacteristicValue.builder()
                .characteristicName("ResponseDescription")
                .value(responseCode.equals("00") ? "Success" : "Failed")
                .build();

        List<CharacteristicValue> characteristicValues = new ArrayList<>();
        characteristicValues.add(codeValue);
        characteristicValues.add(descValue);

        com.safaricom.dxl.sms.transmitter.model.pojo.tibco.Specification specification =
                com.safaricom.dxl.sms.transmitter.model.pojo.tibco.Specification.builder()
                .characteristicValue(characteristicValues)
                .build();

        com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Parts parts =
                com.safaricom.dxl.sms.transmitter.model.pojo.tibcoresponse.Parts.builder()
                .specification(specification)
                .build();

        return Response.builder()
                .parts(parts)
                .build();
    }

    /**
     * Creates a mock TibcoMessage object.
     *
     * @return A mock TibcoMessage object
     */
    public static TibcoMessage mockTibcoMessage() {
        List<Id> id = new ArrayList<>();
        id.add(Id.builder().value("************").build());
        Receiver receiver = Receiver.builder().id(id).build();
        Roles roles = Roles.builder().receiver(receiver).build();
        Trailer trailer = Trailer.builder().text("12345").build();
        Body body = Body.builder().text("Test message").build();
        com.safaricom.dxl.sms.transmitter.model.pojo.tibco.Parts parts =
                com.safaricom.dxl.sms.transmitter.model.pojo.tibco.Parts.builder()
                        .body(body).trailer(trailer).build();
        return TibcoMessage.builder().roles(roles).parts(parts).build();
    }

    /**
     * Creates a mock ApachePayload object.
     *
     * @return A mock ApachePayload object
     */
    public static ApachePayload mockApachePayload() {
        List<TibcoMessage> requests = new ArrayList<>();
        requests.add(mockTibcoMessage());
        return ApachePayload.builder()
                .request(requests)
                .build();
    }

    /**
     * Creates a mock ApacheAuthToken object.
     *
     * @return A mock ApacheAuthToken object
     */
    public static ApacheAuthToken mockApacheAuthToken() {
        return ApacheAuthToken.builder()
                .accessToken("mock-access-token")
                .tokenType("Bearer")
                .expiresIn("3600")
                .build();
    }

    /**
     * Creates a mock WsResponse object.
     *
     * @param code The response code
     * @param message The response message
     * @param body The response body
     * @return A mock WsResponse object
     */
    public static WsResponse mockWsResponse(String code, String message, Object body) {
        WsResponse response = new WsResponse();
        response.setHeader(new WsHeader(
                UUID.randomUUID().toString(),
                Integer.parseInt(code),
                code,
                message,
                LocalDateTime.now().toString()
        ));
        response.setBody(body);
        return response;
    }

    /**
     * Creates a mock ResponseEntity object.
     *
     * @param body The response body
     * @param status The HTTP status
     * @return A mock ResponseEntity object
     */
    public static ResponseEntity<Object> mockResponseEntity(Object body, HttpStatus status) {
        return ResponseEntity.status(status).body(body);
    }

    /**
     * Creates a mock headers map.
     *
     * @return A map of mock headers
     */
    public static Map<String, String> mockHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put(X_CONVERSATION_ID, UUID.randomUUID().toString());
        headers.put(X_SOURCE_SYSTEM, "TEST_SYSTEM");
        headers.put(X_APP, "TEST_APP");
        headers.put(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE);
        headers.put(X_SOURCE_TIMESTAMP, LocalDateTime.now().toString());
        headers.put(X_SOURCE_DIVISION, "TEST_DIVISION");
        headers.put(X_MESSAGE_ID, UUID.randomUUID().toString());
        headers.put(X_SOURCE_COUNTRYCODE, "KE");
        headers.put(X_DEVICE_ID, UUID.randomUUID().toString());
        headers.put(X_SOURCE_OPERATOR, "SAFARICOM");
        return headers;
    }

    public static Map<String, String> mockZuriHeaders() {
        Map<String, String> headers = new HashMap<>();
        String uuid = UUID.randomUUID().toString();
        headers.put(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE);
        headers.put(HttpHeaders.ACCEPT_ENCODING, MediaType.APPLICATION_JSON_VALUE);
        headers.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        headers.put(X_CONVERSATION_ID, "test-conversation-id");
        headers.put(X_MESSAGE_ID, uuid);
        headers.put("x-api-key", uuid);
        headers.put(X_SOURCE_SYSTEM, "zuri");
        headers.put(X_APP, "zuri");
        headers.put(X_SOURCE_TIMESTAMP, LocalDateTime.now().toString());
        headers.put(X_VERSION, "1.0");
        headers.put(X_DEVICE_TOKEN, uuid);
        headers.put(HEADER_AUTHORIZATION, "Basic ZGV2ZWxvcG1lbnQ6a3MlcnJSK1NKNSZDWFAzag==");
        headers.put(X_MSISDN, "************");
        return headers;
    }

    /**
     * Creates a mock LocalDateTime for testing.
     *
     * @return A fixed LocalDateTime
     */
    public static LocalDateTime mockDateTime() {
        return LocalDateTime.of(2023, 1, 1, 12, 0, 0);
    }

    /**
     * Creates a mock Mono of WsResponse.
     *
     * @param code The response code
     * @param message The response message
     * @param body The response body
     * @return A Mono emitting a mock WsResponse
     */
    public static Mono<WsResponse> mockWsResponseMono(String code, String message, Object body) {
        return Mono.just(mockWsResponse(code, message, body));
    }
}
