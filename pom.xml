<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.safaricom.dxl</groupId>
        <artifactId>dxl-webflux-starter-parent</artifactId>
        <version>1.4.5</version>
        <relativePath/>
    </parent>
    <artifactId>ms-sms-transmitter</artifactId>
    <version>1.0.1</version>
	<packaging>jar</packaging>

    <name>ms-sms-transmitter</name>
    <description>SMS transmission integrator</description>
    <properties>
        <!-- Only exclude generated code and configuration from Sonar analysis -->
        <sonar.exclusions>
            **/MsSmsTransmitterApplication.java
        </sonar.exclusions>
        <!-- Only exclude configuration, models, and application entry point from coverage requirements -->
        <sonar.coverage.exclusions>
            **/config/*.java,
            **/exception/*.java,
            **/model/**/*.java,
            **/utils/*.java,
            **/MsSmsTransmitterApplication.java
        </sonar.coverage.exclusions>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        </dependency>
<!--        Queue-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.rabbitmq</groupId>
                    <artifactId>amqp-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
            <version>5.20.0</version>
        </dependency>
    </dependencies>
    <repositories>
<!--        Online-->
        <repository>
            <id>dxl-releases</id>
            <name>safaricom-dxl-releases</name>
            <url>https://jfrog.safaricom.co.ke/artifactory/dxl-releases/</url>
        </repository>
        <repository>
            <id>dxl-snapshots</id>
            <name>safaricom-dxl-snapshot</name>
            <url>https://jfrog.safaricom.co.ke/artifactory/dxl-snapshot/</url>
        </repository>
    </repositories>
</project>